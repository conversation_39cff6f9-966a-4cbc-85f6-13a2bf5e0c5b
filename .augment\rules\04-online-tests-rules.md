---
type: "conditional_apply"
description: "在线测试功能和AI集成规范"
---

# 在线测试功能规范

## 测试类型和功能规划
```typescript
// 测试类型配置
const TEST_TYPES = {
  tarot: {
    name: '塔罗牌测试',
    description: '通过选择塔罗牌获得人生指导',
    duration: '5-10分钟',
    questions: 8,
    aiAnalysis: true,
    shareEnabled: true
  },
  astrology: {
    name: '星座性格测试',
    description: '深度分析你的星座特质和运势',
    duration: '3-5分钟',
    questions: 12,
    aiAnalysis: true,
    shareEnabled: true
  },
  numerology: {
    name: '数字命理测试',
    description: '通过生日和姓名解读生命密码',
    duration: '2-3分钟',
    questions: 6,
    aiAnalysis: true,
    shareEnabled: true
  },
  crystal: {
    name: '水晶能量测试',
    description: '找到最适合你的水晶和能量石',
    duration: '4-6分钟',
    questions: 10,
    aiAnalysis: true,
    shareEnabled: true
  },
  palmistry: {
    name: '手相解读测试',
    description: '通过手相特征解读性格和命运',
    duration: '6-8分钟',
    questions: 15,
    aiAnalysis: true,
    shareEnabled: true
  },
  dream: {
    name: '梦境解析测试',
    description: '解读梦境符号和潜意识信息',
    duration: '5-7分钟',
    questions: 8,
    aiAnalysis: true,
    shareEnabled: true
  }
};
```

## AI服务集成架构
```typescript
// AI服务提供商配置
const AI_PROVIDERS = {
  qwen: {
    name: '通义千问',
    endpoint: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
    model: 'qwen-turbo',
    priority: 1,
    rateLimit: 100, // 每分钟请求数
    timeout: 30000,
    features: ['text-generation', 'conversation']
  },
  doubao: {
    name: '豆包',
    endpoint: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
    model: 'ep-********-xxxxx',
    priority: 2,
    rateLimit: 80,
    timeout: 25000,
    features: ['text-generation', 'conversation']
  },
  zhipu: {
    name: '智谱AI',
    endpoint: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
    model: 'glm-4',
    priority: 3,
    rateLimit: 60,
    timeout: 20000,
    features: ['text-generation', 'conversation', 'multilingual']
  }
};

// AI服务路由策略
const AI_ROUTING_STRATEGY = {
  primary: 'qwen',           // 主要服务
  fallback: ['doubao', 'zhipu'], // 备用服务
  loadBalancing: 'round-robin',   // 负载均衡策略
  healthCheck: true,              // 健康检查
  retryAttempts: 3,              // 重试次数
  circuitBreaker: true           // 熔断器
};
```

## 测试流程设计
```typescript
// 测试流程状态机
const TEST_FLOW_STATES = {
  INIT: 'init',           // 初始化
  STARTED: 'started',     // 开始测试
  IN_PROGRESS: 'in_progress', // 进行中
  ANALYZING: 'analyzing', // AI分析中
  COMPLETED: 'completed', // 完成
  ERROR: 'error'          // 错误
};

// 测试数据结构
interface TestSession {
  id: string;
  userId?: string;
  testType: string;
  locale: string;
  state: TestFlowState;
  answers: TestAnswer[];
  result?: TestResult;
  aiAnalysis?: AIAnalysisResult;
  createdAt: Date;
  completedAt?: Date;
  shareToken?: string;
}

interface TestAnswer {
  questionId: string;
  answer: string | string[] | number;
  timestamp: Date;
}

interface TestResult {
  id: string;
  testSessionId: string;
  summary: string;
  details: ResultDetail[];
  recommendations: string[];
  aiInsights: string;
  shareableContent: ShareableContent;
}
```

## AI提示词工程
```typescript
// 提示词模板系统
const PROMPT_TEMPLATES = {
  tarot: {
    system: `你是一位专业的塔罗牌解读师，具有深厚的神秘学知识。
    请根据用户选择的塔罗牌和问题，提供准确、有洞察力的解读。
    解读应该包含：
    1. 牌面含义解释
    2. 当前情况分析
    3. 未来趋势预测
    4. 具体建议和指导
    
    请用温暖、专业的语调，避免过于绝对的预言。`,
    
    user: `用户选择了以下塔罗牌：{cards}
    用户的问题类型：{questionType}
    用户的具体情况：{userContext}
    
    请提供详细的塔罗牌解读。`
  },
  
  astrology: {
    system: `你是一位专业的占星师，精通西方占星学和心理占星学。
    请根据用户的星座信息和测试答案，提供个性化的性格分析和运势指导。
    分析应该包含：
    1. 性格特质分析
    2. 优势和潜力
    3. 需要注意的方面
    4. 近期运势趋势
    5. 人际关系建议
    
    请保持客观和建设性的态度。`,
    
    user: `用户的星座信息：{zodiacInfo}
    测试答案：{testAnswers}
    用户关注的领域：{focusAreas}
    
    请提供个性化的占星分析。`
  },
  
  numerology: {
    system: `你是一位数字命理学专家，精通生命数字、表达数字等各种数字命理系统。
    请根据用户的生日、姓名等信息，计算并解读相关的生命数字。
    解读应该包含：
    1. 生命数字含义
    2. 性格特质分析
    3. 人生使命和目标
    4. 幸运数字和颜色
    5. 发展建议
    
    请提供准确的计算和深入的解读。`,
    
    user: `用户信息：
    生日：{birthDate}
    姓名：{name}
    测试答案：{answers}
    
    请计算并解读用户的数字命理。`
  }
};
```

## 组件架构设计
```typescript
// 测试系统模块
const TEST_MODULE_STRUCTURE = {
  components: {
    'TestContainer.tsx': '测试容器组件',
    'QuestionCard.tsx': '问题卡片',
    'ProgressBar.tsx': '进度条',
    'ResultDisplay.tsx': '结果展示',
    'ShareResult.tsx': '结果分享',
    'TestHistory.tsx': '测试历史'
  },
  
  tests: {
    'TarotTest.tsx': '塔罗牌测试',
    'AstrologyTest.tsx': '星座测试',
    'NumerologyTest.tsx': '数字命理测试',
    'CrystalTest.tsx': '水晶测试',
    'PalmistryTest.tsx': '手相测试',
    'DreamTest.tsx': '梦境解析测试'
  },
  
  hooks: {
    'useTestFlow.ts': '测试流程管理',
    'useAIAnalysis.ts': 'AI分析Hook',
    'useTestResults.ts': '测试结果管理',
    'useTestHistory.ts': '测试历史'
  },
  
  services: {
    'aiService.ts': 'AI服务集成',
    'testService.ts': '测试数据服务',
    'analyticsService.ts': '分析服务'
  }
};
```

## AI服务集成实现
```typescript
// AI服务管理器
class AIServiceManager {
  private providers: Map<string, AIProvider> = new Map();
  private currentProvider: string = 'qwen';
  
  // 初始化AI服务
  async initialize() {
    // 注册所有AI服务提供商
    this.registerProvider('qwen', new QwenProvider());
    this.registerProvider('doubao', new DoubaoProvider());
    this.registerProvider('zhipu', new ZhipuProvider());
    
    // 健康检查
    await this.healthCheck();
  }
  
  // 智能路由请求
  async generateAnalysis(
    testType: string,
    userAnswers: TestAnswer[],
    locale: string
  ): Promise<AIAnalysisResult> {
    const prompt = this.buildPrompt(testType, userAnswers, locale);
    
    for (let attempt = 0; attempt < 3; attempt++) {
      try {
        const provider = this.selectProvider();
        const result = await provider.generate(prompt);
        
        // 记录成功请求
        await this.logRequest(provider.name, 'success', result);
        
        return this.parseResult(result);
        
      } catch (error) {
        // 记录失败请求
        await this.logRequest(this.currentProvider, 'error', error);
        
        // 切换到备用服务
        this.switchToFallback();
        
        if (attempt === 2) {
          throw new Error('All AI providers failed');
        }
      }
    }
  }
  
  // 构建提示词
  private buildPrompt(
    testType: string,
    answers: TestAnswer[],
    locale: string
  ): string {
    const template = PROMPT_TEMPLATES[testType];
    const userContext = this.buildUserContext(answers);
    
    return template.user.replace('{answers}', JSON.stringify(answers))
                        .replace('{userContext}', userContext)
                        .replace('{locale}', locale);
  }
  
  // 服务健康检查
  private async healthCheck(): Promise<void> {
    for (const [name, provider] of this.providers) {
      try {
        await provider.healthCheck();
        console.log(`AI Provider ${name} is healthy`);
      } catch (error) {
        console.warn(`AI Provider ${name} is unhealthy:`, error);
      }
    }
  }
}
```

## 数据库设计
```sql
-- 测试配置表
CREATE TABLE test_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  test_type VARCHAR(50) NOT NULL,
  locale VARCHAR(10) NOT NULL,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  questions JSONB NOT NULL,
  settings JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 测试会话表
CREATE TABLE test_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  test_type VARCHAR(50) NOT NULL,
  locale VARCHAR(10) NOT NULL,
  state VARCHAR(20) DEFAULT 'init',
  answers JSONB,
  metadata JSONB,
  share_token VARCHAR(100) UNIQUE,
  created_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP
);

-- 测试结果表
CREATE TABLE test_results (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  test_session_id UUID REFERENCES test_sessions(id),
  summary TEXT,
  details JSONB,
  ai_analysis TEXT,
  recommendations JSONB,
  shareable_content JSONB,
  view_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);

-- AI服务日志表
CREATE TABLE ai_service_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider VARCHAR(50) NOT NULL,
  test_session_id UUID REFERENCES test_sessions(id),
  prompt_tokens INTEGER,
  completion_tokens INTEGER,
  total_tokens INTEGER,
  response_time INTEGER,
  status VARCHAR(20),
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## API接口设计
```typescript
// 测试相关API
const TEST_API_ROUTES = {
  // 测试管理
  'GET /api/tests': '获取测试列表',
  'GET /api/tests/[testType]': '获取测试配置',
  'GET /api/tests/[testType]/questions': '获取测试问题',
  
  // 测试执行
  'POST /api/tests/[testType]/start': '开始测试',
  'POST /api/tests/[testType]/submit': '提交测试答案',
  'GET /api/tests/result/[id]': '获取测试结果',
  
  // 用户历史
  'GET /api/tests/history': '获取用户测试历史',
  'GET /api/tests/share/[token]': '获取分享的测试结果',
  
  // AI服务
  'POST /api/ai/analyze': 'AI分析请求',
  'GET /api/ai/health': 'AI服务健康检查'
};
```

## 缓存策略
```typescript
// 测试相关缓存配置
const TEST_CACHE_CONFIG = {
  TEST_CONFIGS: '2h',         // 测试配置
  AI_RESPONSES: '7d',         // AI响应结果
  TEST_RESULTS: '30d',        // 测试结果
  SHARED_RESULTS: '90d',      // 分享的结果
  USER_SESSIONS: '24h'        // 用户会话
};
```

## 性能优化
- **AI响应缓存**：相同输入的AI分析结果缓存7天
- **异步处理**：AI分析异步进行，不阻塞用户体验
- **负载均衡**：多个AI服务分担负载
- **故障转移**：主服务故障时自动切换备用服务
- **请求限流**：防止AI服务过载
