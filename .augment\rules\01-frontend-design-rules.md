---
type: "conditional_apply"
description: "前端设计系统和UI组件规范"
---

# 前端设计系统规范

## 整体设计理念
- **神秘优雅**：融合现代设计与神秘学美学
- **信任感建立**：专业、权威的视觉呈现
- **文化包容性**：适应不同文化背景的用户
- **情感共鸣**：营造神秘、温暖、智慧的氛围

## 颜色系统
```javascript
// tailwind.config.js主题配置
theme: {
  extend: {
    colors: {
      // 主色调 - 神秘紫色系
      mystical: {
        50: '#faf7ff',   // 极浅紫，背景色
        100: '#f3ecff',  // 浅紫，卡片背景
        200: '#e9d8ff',  // 淡紫，悬停状态
        300: '#d8b9ff',  // 中浅紫，边框
        400: '#c084fc',  // 中紫，次要按钮
        500: '#a855f7',  // 标准紫，主按钮
        600: '#9333ea',  // 深紫，按钮悬停
        700: '#7c3aed',  // 更深紫，激活状态
        800: '#6b21a8',  // 很深紫，文字
        900: '#581c87',  // 最深紫，标题
      },
      
      // 辅助色 - 黄金色系
      gold: {
        50: '#fffbeb',   // 极浅金
        100: '#fef3c7',  // 浅金
        200: '#fde68a',  // 淡金
        300: '#fcd34d',  // 中浅金
        400: '#fbbf24',  // 中金
        500: '#f59e0b',  // 标准金，强调色
        600: '#d97706',  // 深金
        700: '#b45309',  // 更深金
        800: '#92400e',  // 很深金
        900: '#78350f',  // 最深金
      },
      
      // 深色系 - 神秘黑色系
      dark: {
        50: '#f8fafc',   // 极浅灰
        100: '#f1f5f9',  // 浅灰
        200: '#e2e8f0',  // 淡灰
        300: '#cbd5e1',  // 中浅灰
        400: '#94a3b8',  // 中灰
        500: '#64748b',  // 标准灰
        600: '#475569',  // 深灰
        700: '#334155',  // 更深灰
        800: '#1e293b',  // 很深灰，深色模式背景
        900: '#0f172a',  // 最深灰，深色模式主背景
      },
      
      // 星座色彩系统
      zodiac: {
        fire: '#ff6b6b',      // 火象星座 - 红色系
        earth: '#51cf66',     // 土象星座 - 绿色系
        air: '#74c0fc',       // 风象星座 - 蓝色系
        water: '#845ef7',     // 水象星座 - 紫色系
      }
    },
    
    fontFamily: {
      // 主要字体 - 现代无衬线
      sans: ['Inter', 'Noto Sans', 'system-ui', 'sans-serif'],
      
      // 标题字体 - 优雅衬线
      serif: ['Playfair Display', 'Noto Serif', 'Georgia', 'serif'],
      
      // 神秘字体 - 装饰性
      mystical: ['Cinzel', 'Trajan Pro', 'serif'],
      
      // 等宽字体 - 代码/数据
      mono: ['JetBrains Mono', 'Fira Code', 'Consolas', 'monospace'],
    },
    
    animation: {
      // 神秘学主题动画
      'mystical-glow': 'mysticalGlow 3s ease-in-out infinite alternate',
      'tarot-flip': 'tarotFlip 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
      'crystal-shine': 'crystalShine 2s ease-in-out infinite',
      'star-twinkle': 'starTwinkle 1.5s ease-in-out infinite alternate',
      'fade-in': 'fadeIn 0.6s ease-out',
      'slide-up': 'slideUp 0.5s ease-out',
      'scale-in': 'scaleIn 0.3s ease-out',
      'float': 'float 3s ease-in-out infinite',
    },
    
    keyframes: {
      mysticalGlow: {
        '0%': { 
          boxShadow: '0 0 20px rgba(168, 85, 247, 0.3)',
          transform: 'scale(1)'
        },
        '100%': { 
          boxShadow: '0 0 40px rgba(168, 85, 247, 0.6)',
          transform: 'scale(1.02)'
        }
      },
      tarotFlip: {
        '0%': { transform: 'rotateY(0deg)' },
        '50%': { transform: 'rotateY(90deg)' },
        '100%': { transform: 'rotateY(0deg)' }
      },
      crystalShine: {
        '0%, 100%': { opacity: '0.8' },
        '50%': { opacity: '1', transform: 'scale(1.05)' }
      },
      starTwinkle: {
        '0%': { opacity: '0.5', transform: 'scale(0.8)' },
        '100%': { opacity: '1', transform: 'scale(1.2)' }
      },
      fadeIn: {
        '0%': { opacity: '0', transform: 'translateY(10px)' },
        '100%': { opacity: '1', transform: 'translateY(0)' }
      },
      slideUp: {
        '0%': { transform: 'translateY(100%)' },
        '100%': { transform: 'translateY(0)' }
      },
      scaleIn: {
        '0%': { transform: 'scale(0.9)', opacity: '0' },
        '100%': { transform: 'scale(1)', opacity: '1' }
      },
      float: {
        '0%, 100%': { transform: 'translateY(0px)' },
        '50%': { transform: 'translateY(-10px)' }
      }
    },
    
    boxShadow: {
      // 神秘学主题阴影
      'mystical': '0 10px 25px -3px rgba(168, 85, 247, 0.1), 0 4px 6px -2px rgba(168, 85, 247, 0.05)',
      'mystical-lg': '0 20px 40px -4px rgba(168, 85, 247, 0.15), 0 8px 16px -4px rgba(168, 85, 247, 0.1)',
      'gold': '0 10px 25px -3px rgba(245, 158, 11, 0.1), 0 4px 6px -2px rgba(245, 158, 11, 0.05)',
      'inner-mystical': 'inset 0 2px 4px 0 rgba(168, 85, 247, 0.1)',
    }
  }
}
```

## 页面布局设计规范

### 整体布局结构
```typescript
// 页面布局层次
const LAYOUT_HIERARCHY = {
  // 主布局容器
  container: {
    maxWidth: '1440px',
    margin: '0 auto',
    padding: { mobile: '1rem', tablet: '1.5rem', desktop: '2rem' }
  },
  
  // 页面区域划分
  sections: {
    header: { height: '80px', sticky: true, zIndex: 50 },
    hero: { minHeight: '60vh', background: 'gradient' },
    content: { padding: '4rem 0', background: 'white/dark' },
    sidebar: { width: '320px', sticky: true },
    footer: { padding: '3rem 0', background: 'dark' }
  },
  
  // 网格系统
  grid: {
    columns: { mobile: 1, tablet: 2, desktop: 3, wide: 4 },
    gap: { mobile: '1rem', tablet: '1.5rem', desktop: '2rem' },
    breakpoints: {
      sm: '640px',
      md: '768px', 
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px'
    }
  }
};
```

## 卡片设计系统
```typescript
// 卡片组件设计规范
const CARD_DESIGN_SYSTEM = {
  // 基础卡片
  base: {
    background: 'white dark:dark-800',
    border: '1px solid mystical-200 dark:dark-700',
    borderRadius: 'xl',
    shadow: 'mystical',
    padding: '1.5rem',
    transition: 'all 0.3s ease'
  },
  
  // 卡片变体
  variants: {
    // 博客文章卡片
    blog: {
      image: { aspectRatio: '16:9', borderRadius: 'lg' },
      title: { fontSize: 'xl', fontWeight: 'bold', color: 'mystical-900' },
      excerpt: { fontSize: 'sm', color: 'mystical-600', lines: 3 },
      meta: { fontSize: 'xs', color: 'mystical-500' },
      hover: 'transform scale-105 shadow-mystical-lg'
    },
    
    // 商品卡片
    product: {
      image: { aspectRatio: '1:1', borderRadius: 'lg' },
      title: { fontSize: 'lg', fontWeight: 'semibold' },
      price: { fontSize: 'xl', fontWeight: 'bold', color: 'gold-600' },
      rating: { stars: 'gold-400', text: 'mystical-600' },
      button: { width: 'full', variant: 'mystical' }
    },
    
    // 测试结果卡片
    test: {
      background: 'gradient-to-br from-mystical-50 to-gold-50',
      icon: { size: '3rem', color: 'mystical-600' },
      title: { fontSize: '2xl', fontWeight: 'bold', textAlign: 'center' },
      description: { fontSize: 'base', textAlign: 'center', color: 'mystical-700' },
      animation: 'mystical-glow'
    },
    
    // 塔罗牌卡片
    tarot: {
      aspectRatio: '2:3',
      background: 'gradient-mystical',
      border: '2px solid gold-400',
      borderRadius: '2xl',
      shadow: 'mystical-lg',
      hover: 'animate-tarot-flip',
      backface: 'mystical-pattern'
    }
  }
};
```

## 按钮设计系统
```typescript
// 按钮组件设计
const BUTTON_DESIGN = {
  // 基础样式
  base: {
    fontWeight: 'medium',
    borderRadius: 'lg',
    transition: 'all 0.2s ease',
    focus: 'ring-2 ring-mystical-500 ring-offset-2'
  },
  
  // 尺寸变体
  sizes: {
    xs: { padding: '0.5rem 0.75rem', fontSize: 'xs' },
    sm: { padding: '0.75rem 1rem', fontSize: 'sm' },
    md: { padding: '1rem 1.5rem', fontSize: 'base' },
    lg: { padding: '1.25rem 2rem', fontSize: 'lg' },
    xl: { padding: '1.5rem 2.5rem', fontSize: 'xl' }
  },
  
  // 颜色变体
  variants: {
    primary: {
      background: 'mystical-600',
      color: 'white',
      hover: 'mystical-700',
      shadow: 'mystical'
    },
    secondary: {
      background: 'mystical-100',
      color: 'mystical-700',
      hover: 'mystical-200',
      border: 'mystical-300'
    },
    gold: {
      background: 'gold-500',
      color: 'white',
      hover: 'gold-600',
      shadow: 'gold'
    },
    ghost: {
      background: 'transparent',
      color: 'mystical-600',
      hover: 'mystical-50',
      border: 'mystical-300'
    }
  }
};
```

## 响应式设计规范

### 断点系统
```typescript
// 响应式断点配置
const RESPONSIVE_BREAKPOINTS = {
  mobile: {
    max: '767px',
    container: '100%',
    padding: '1rem',
    columns: 1,
    navigation: 'hamburger'
  },
  tablet: {
    min: '768px',
    max: '1023px',
    container: '768px',
    padding: '1.5rem',
    columns: 2,
    navigation: 'horizontal'
  },
  desktop: {
    min: '1024px',
    max: '1279px',
    container: '1024px',
    padding: '2rem',
    columns: 3,
    navigation: 'horizontal-with-dropdown'
  },
  wide: {
    min: '1280px',
    container: '1280px',
    padding: '2rem',
    columns: 4,
    navigation: 'full-horizontal'
  }
};
```

## 深色模式设计
```typescript
// 深色模式配色方案
const DARK_MODE_THEME = {
  // 背景色系
  backgrounds: {
    primary: 'dark-900',      // 主背景
    secondary: 'dark-800',    // 卡片背景
    tertiary: 'dark-700',     // 悬停背景
    overlay: 'dark-900/80'    // 遮罩背景
  },
  
  // 文字色系
  text: {
    primary: 'white',         // 主要文字
    secondary: 'dark-200',    // 次要文字
    tertiary: 'dark-400',     // 辅助文字
    accent: 'mystical-400'    // 强调文字
  },
  
  // 边框色系
  borders: {
    primary: 'dark-600',      // 主要边框
    secondary: 'dark-700',    // 次要边框
    accent: 'mystical-600'    // 强调边框
  }
};
```

## 组件样式规范
- **一致性原则**：所有组件遵循统一的设计语言
- **层次结构**：清晰的视觉层级和信息架构
- **交互反馈**：悬停、点击、加载状态的视觉反馈
- **响应式设计**：适配所有设备尺寸
- **深色模式**：完整的深色主题支持
- **可访问性**：符合WCAG 2.1 AA标准
