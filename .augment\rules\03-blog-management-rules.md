---
type: "conditional_apply"
description: "博文管理系统和内容导入规范"
---

# 博文管理系统规范

## 博文生命周期管理
```typescript
// 博文状态枚举
enum PostStatus {
  DRAFT = 'draft',           // 草稿
  PENDING = 'pending',       // 待审核
  SCHEDULED = 'scheduled',   // 定时发布
  PUBLISHED = 'published',   // 已发布
  ARCHIVED = 'archived',     // 已归档
  DELETED = 'deleted'        // 已删除（软删除）
}

// 博文工作流
const POST_WORKFLOW = {
  // 创建流程
  creation: {
    import: 'AI生成 -> 导入 -> 解析 -> 预览',
    manual: '手动创建 -> 编辑 -> 预览',
    bulk: '批量导入 -> 解析 -> 批量预览'
  },
  
  // 审核流程
  review: {
    content: 'AI内容检查 -> 人工审核 -> SEO优化',
    seo: 'SEO分析 -> 关键词优化 -> 元数据完善',
    quality: '内容质量检查 -> 图片优化 -> 链接检查'
  },
  
  // 发布流程
  publishing: {
    immediate: '立即发布 -> 索引提交 -> 社交分享',
    scheduled: '定时发布 -> 自动发布 -> 通知推送',
    batch: '批量发布 -> 分批处理 -> 进度跟踪'
  }
};
```

## AI内容导入系统
```typescript
// AI生成内容的标准格式
interface AIGeneratedPost {
  // Frontmatter元数据
  frontmatter: {
    title: string;
    description: string;
    keywords: string[];
    category: string;
    tags: string[];
    locale: string;
    publishDate?: string;
    author?: string;
    coverImage?: string;
    seoTitle?: string;
    seoDescription?: string;
  };
  
  // 文章内容
  content: string;
  
  // 图片信息
  images?: {
    url: string;
    alt: string;
    caption?: string;
    position: 'cover' | 'inline' | 'gallery';
  }[];
  
  // AI生成信息
  aiMetadata: {
    model: string;
    generatedAt: string;
    prompt?: string;
    confidence?: number;
  };
}

// 导入配置
const IMPORT_CONFIG = {
  // 支持的文件格式
  supportedFormats: ['.md', '.mdx', '.txt'],
  
  // 批量导入设置
  batchImport: {
    maxFiles: 100,
    maxFileSize: '10MB',
    concurrency: 5,
    timeout: 30000
  },
  
  // 内容解析规则
  parsing: {
    frontmatterDelimiter: '---',
    imagePattern: /!\[([^\]]*)\]\(([^)]+)\)/g,
    headingPattern: /^#{1,6}\s+(.+)$/gm,
    linkPattern: /\[([^\]]+)\]\(([^)]+)\)/g
  },
  
  // 自动处理选项
  autoProcessing: {
    generateSlug: true,
    optimizeImages: true,
    extractKeywords: true,
    seoAnalysis: true,
    duplicateCheck: true
  }
};
```

## 文件系统组织结构
```typescript
// 博文文件组织规范
const BLOG_FILE_STRUCTURE = {
  // 内容目录结构
  contentDir: 'content/blog/',
  structure: {
    // 按语言组织
    byLocale: {
      'en/': 'English posts',
      'zh/': 'Chinese posts',
      'es/': 'Spanish posts',
      'pt/': 'Portuguese posts',
      'hi/': 'Hindi posts',
      'ja/': 'Japanese posts'
    },
    
    // 按分类组织
    byCategory: {
      'tarot/': 'Tarot related posts',
      'astrology/': 'Astrology posts',
      'numerology/': 'Numerology posts',
      'crystals/': 'Crystal healing posts',
      'general/': 'General mystical content'
    }
  },
  
  // 文件命名规范
  naming: {
    pattern: '{date}-{slug}.{locale}.md',
    example: '2024-01-15-tarot-beginners-guide.en.md',
    slug: 'kebab-case',
    dateFormat: 'YYYY-MM-DD'
  },
  
  // 图片资源组织
  assets: {
    images: 'public/images/blog/{year}/{month}/',
    thumbnails: 'public/images/blog/thumbnails/',
    covers: 'public/images/blog/covers/'
  }
};
```

## 批量导入工具设计
```typescript
// 批量导入服务
class BulkImportService {
  // 扫描导入目录
  async scanImportDirectory(path: string): Promise<ImportCandidate[]> {
    // 1. 扫描指定目录
    // 2. 识别Markdown文件
    // 3. 解析Frontmatter
    // 4. 检查图片资源
    // 5. 返回导入候选列表
  }
  
  // 批量导入处理
  async processBulkImport(
    candidates: ImportCandidate[],
    options: ImportOptions
  ): Promise<ImportResult[]> {
    const results: ImportResult[] = [];
    
    for (const candidate of candidates) {
      try {
        // 1. 解析Markdown内容
        const parsed = await this.parseMarkdown(candidate.filePath);
        
        // 2. 处理图片资源
        const processedImages = await this.processImages(parsed.images);
        
        // 3. 生成SEO数据
        const seoData = await this.generateSEO(parsed.content);
        
        // 4. 创建博文记录
        const post = await this.createPost({
          ...parsed,
          images: processedImages,
          seo: seoData,
          status: options.autoPublish ? 'published' : 'draft'
        });
        
        results.push({
          success: true,
          post,
          originalFile: candidate.filePath
        });
        
      } catch (error) {
        results.push({
          success: false,
          error: error.message,
          originalFile: candidate.filePath
        });
      }
    }
    
    return results;
  }
  
  // Markdown解析
  async parseMarkdown(filePath: string): Promise<ParsedContent> {
    // 1. 读取文件内容
    // 2. 解析Frontmatter
    // 3. 解析Markdown内容
    // 4. 提取图片引用
    // 5. 生成目录结构
    // 6. 返回结构化数据
  }
  
  // 图片处理
  async processImages(images: ImageReference[]): Promise<ProcessedImage[]> {
    // 1. 复制图片到正确目录
    // 2. 生成不同尺寸的缩略图
    // 3. 优化图片质量
    // 4. 生成WebP格式
    // 5. 更新图片URL引用
  }
}
```

## 内容质量检查系统
```typescript
// 内容质量检查器
class ContentQualityChecker {
  // 综合质量检查
  async checkContentQuality(post: BlogPost): Promise<QualityReport> {
    const checks = await Promise.all([
      this.checkSEO(post),
      this.checkReadability(post),
      this.checkImages(post),
      this.checkLinks(post),
      this.checkDuplicates(post)
    ]);
    
    return this.generateQualityReport(checks);
  }
  
  // SEO检查
  async checkSEO(post: BlogPost): Promise<SEOCheck> {
    return {
      titleLength: this.checkTitleLength(post.title),
      descriptionLength: this.checkDescriptionLength(post.description),
      keywordDensity: this.checkKeywordDensity(post.content, post.keywords),
      headingStructure: this.checkHeadingStructure(post.content),
      metaTags: this.checkMetaTags(post),
      score: this.calculateSEOScore(post)
    };
  }
  
  // 可读性检查
  async checkReadability(post: BlogPost): Promise<ReadabilityCheck> {
    return {
      wordCount: this.countWords(post.content),
      sentenceLength: this.analyzeSentenceLength(post.content),
      paragraphLength: this.analyzeParagraphLength(post.content),
      readingTime: this.calculateReadingTime(post.content),
      fleschScore: this.calculateFleschScore(post.content)
    };
  }
  
  // 图片检查
  async checkImages(post: BlogPost): Promise<ImageCheck> {
    return {
      altTags: this.checkAltTags(post.images),
      fileSize: this.checkImageSizes(post.images),
      format: this.checkImageFormats(post.images),
      loading: this.checkLazyLoading(post.images)
    };
  }
  
  // 重复内容检查
  async checkDuplicates(post: BlogPost): Promise<DuplicateCheck> {
    // 1. 检查标题重复
    // 2. 检查内容相似度
    // 3. 检查关键词重叠
    // 4. 生成重复度报告
  }
}
```

## 自动化发布系统
```typescript
// 自动发布调度器
class PublishingScheduler {
  // 定时发布
  async schedulePost(postId: string, publishDate: Date): Promise<void> {
    // 1. 创建定时任务
    // 2. 存储发布计划
    // 3. 设置提醒通知
  }
  
  // 批量发布
  async batchPublish(
    postIds: string[],
    options: BatchPublishOptions
  ): Promise<BatchPublishResult> {
    const results = [];
    
    for (const postId of postIds) {
      try {
        // 1. 最终质量检查
        const qualityCheck = await this.finalQualityCheck(postId);
        if (!qualityCheck.passed) {
          throw new Error(`Quality check failed: ${qualityCheck.issues.join(', ')}`);
        }
        
        // 2. 发布文章
        await this.publishPost(postId);
        
        // 3. 提交搜索引擎索引
        await this.submitToSearchEngines(postId);
        
        // 4. 社交媒体分享
        if (options.autoShare) {
          await this.shareToSocialMedia(postId);
        }
        
        // 5. 发送通知
        await this.sendPublishNotification(postId);
        
        results.push({ postId, success: true });
        
        // 6. 发布间隔控制
        if (options.interval) {
          await this.delay(options.interval);
        }
        
      } catch (error) {
        results.push({ 
          postId, 
          success: false, 
          error: error.message 
        });
      }
    }
    
    return { results, summary: this.generateSummary(results) };
  }
  
  // 发布后处理
  async postPublishProcessing(postId: string): Promise<void> {
    // 1. 生成社交媒体卡片
    // 2. 更新站点地图
    // 3. 清理缓存
    // 4. 发送Webhook通知
    // 5. 更新相关文章推荐
  }
}
```

## 实际操作流程

### 方式一：单文件快速导入
1. **准备文件**：AI生成的MD文件 + 相关图片
2. **拖拽导入**：直接拖拽到管理后台
3. **自动处理**：系统自动解析、优化、生成预览
4. **一键发布**：检查无误后一键发布

### 方式二：批量导入处理
1. **文件准备**：将多个MD文件和图片放入指定文件夹
2. **批量扫描**：系统扫描文件夹识别所有内容
3. **批量预览**：显示所有待导入文章的预览
4. **批量处理**：选择需要的文章进行批量导入和发布

### 方式三：监控目录自动导入
1. **设置监控**：配置监控的文件夹路径
2. **自动检测**：系统定期检查新增文件
3. **自动处理**：新文件自动导入、解析、优化
4. **通知审核**：处理完成后通知管理员审核发布

## API接口设计
```typescript
// 博客管理API
const BLOG_API_ROUTES = {
  // 基础CRUD
  'GET /api/blog': '获取文章列表',
  'GET /api/blog/[slug]': '获取文章详情',
  'POST /api/blog/create': '创建文章',
  'PUT /api/blog/update/[id]': '更新文章',
  'DELETE /api/blog/delete/[id]': '删除文章',
  
  // 导入相关
  'POST /api/blog/import': '单文件导入',
  'POST /api/blog/bulk-import': '批量导入',
  'GET /api/blog/import-status/[batchId]': '导入状态查询',
  
  // 发布相关
  'POST /api/blog/publish/[id]': '发布文章',
  'POST /api/blog/schedule/[id]': '定时发布',
  'POST /api/blog/batch-publish': '批量发布',
  
  // 质量检查
  'POST /api/blog/quality-check/[id]': '质量检查',
  'POST /api/blog/seo-analysis/[id]': 'SEO分析',
  
  // 分类管理
  'GET /api/blog/categories': '获取分类列表',
  'POST /api/blog/categories': '创建分类',
  'PUT /api/blog/categories/[id]': '更新分类'
};
```

## 数据库扩展表结构
```sql
-- 博文导入记录表
CREATE TABLE blog_imports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  batch_id VARCHAR(100) NOT NULL,
  original_file_path VARCHAR(500),
  file_name VARCHAR(255),
  import_status VARCHAR(20) DEFAULT 'pending',
  blog_post_id UUID REFERENCES blog_posts(id),
  error_message TEXT,
  ai_metadata JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 博文质量检查表
CREATE TABLE blog_quality_checks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  blog_post_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
  check_type VARCHAR(50) NOT NULL,
  score INTEGER CHECK (score >= 0 AND score <= 100),
  issues JSONB,
  suggestions JSONB,
  checked_at TIMESTAMP DEFAULT NOW()
);

-- 博文发布计划表
CREATE TABLE blog_publish_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  blog_post_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
  scheduled_at TIMESTAMP NOT NULL,
  status VARCHAR(20) DEFAULT 'scheduled',
  auto_share BOOLEAN DEFAULT false,
  published_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```
