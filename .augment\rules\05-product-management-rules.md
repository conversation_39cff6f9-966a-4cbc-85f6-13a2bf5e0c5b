---
type: "conditional_apply"
description: "商品管理系统和电商功能规范"
---

# 商品管理系统规范

## 商品管理功能规划
```typescript
// 商品管理核心功能
const PRODUCT_MANAGEMENT_FEATURES = {
  // 商品基础管理
  product_crud: {
    create: '创建新商品',
    read: '查看商品详情',
    update: '编辑商品信息',
    delete: '删除商品（软删除）',
    bulk_operations: '批量操作'
  },
  
  // 库存管理
  inventory: {
    stock_tracking: '库存跟踪',
    low_stock_alerts: '低库存提醒',
    inventory_movements: '库存变动记录',
    bulk_update: '批量库存更新'
  },
  
  // 分类管理
  categories: {
    category_tree: '分类树管理',
    drag_drop_sort: '拖拽排序',
    multi_level: '多级分类',
    seo_optimization: 'SEO优化'
  },
  
  // 订单管理
  orders: {
    order_list: '订单列表',
    order_details: '订单详情',
    status_update: '状态更新',
    payment_tracking: '支付跟踪'
  },
  
  // 图片管理
  media: {
    image_upload: '图片上传',
    image_optimization: '图片优化',
    gallery_management: '图片库管理',
    bulk_upload: '批量上传'
  }
};
```

## 商品类型配置
```typescript
// 玄学商品分类
const PRODUCT_CATEGORIES = {
  tarot: {
    name: '塔罗牌',
    subcategories: [
      'classic-tarot', // 经典塔罗
      'oracle-cards', // 神谕卡
      'tarot-sets', // 塔罗套装
      'tarot-books' // 塔罗书籍
    ],
    attributes: ['deck_size', 'language', 'artist', 'publisher']
  },
  
  crystals: {
    name: '水晶饰品',
    subcategories: [
      'raw-crystals', // 原石
      'jewelry', // 首饰
      'home-decor', // 家居装饰
      'healing-sets' // 疗愈套装
    ],
    attributes: ['crystal_type', 'size', 'color', 'chakra']
  },
  
  books: {
    name: '玄学书籍',
    subcategories: [
      'astrology-books', // 占星书籍
      'tarot-guides', // 塔罗指南
      'numerology', // 数字命理
      'spirituality' // 灵性成长
    ],
    attributes: ['author', 'language', 'pages', 'publisher']
  },
  
  accessories: {
    name: '占星配件',
    subcategories: [
      'altar-tools', // 祭坛工具
      'incense', // 香薰
      'candles', // 蜡烛
      'meditation' // 冥想用品
    ],
    attributes: ['material', 'size', 'scent', 'color']
  }
};
```

## 商品表单设计
```typescript
// 商品创建/编辑表单结构
interface ProductFormData {
  // 基础信息
  name: string;
  slug: string;
  description: string;
  shortDescription?: string;
  
  // 价格和库存
  price: number;
  compareAtPrice?: number;
  sku: string;
  inventory: number;
  trackInventory: boolean;
  
  // 分类和标签
  categoryId: string;
  tags: string[];
  
  // 图片
  images: ProductImage[];
  
  // SEO
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  
  // 状态
  isActive: boolean;
  isFeatured: boolean;
  
  // 多语言
  locale: string;
  translations?: Record<string, Partial<ProductFormData>>;
  
  // 商品变体（可选）
  variants?: ProductVariant[];
  
  // 自定义属性
  attributes?: Record<string, any>;
}

// 商品变体结构
interface ProductVariant {
  id?: string;
  name: string;
  sku: string;
  price?: number;
  inventory: number;
  attributes: Record<string, string>; // 如：{ color: 'red', size: 'M' }
  isActive: boolean;
}
```

## 库存管理逻辑
```typescript
// 库存管理服务
class InventoryService {
  // 库存检查
  async checkStock(productId: string, variantId?: string): Promise<number> {
    // 检查商品或变体的当前库存
  }
  
  // 库存扣减
  async reduceStock(
    productId: string, 
    quantity: number, 
    reason: string,
    variantId?: string
  ): Promise<void> {
    // 扣减库存并记录变动
  }
  
  // 库存增加
  async addStock(
    productId: string, 
    quantity: number, 
    reason: string,
    variantId?: string
  ): Promise<void> {
    // 增加库存并记录变动
  }
  
  // 低库存提醒
  async getLowStockProducts(threshold: number = 10): Promise<Product[]> {
    // 获取低库存商品列表
  }
  
  // 库存变动历史
  async getInventoryHistory(
    productId: string,
    variantId?: string
  ): Promise<InventoryMovement[]> {
    // 获取库存变动历史
  }
}
```

## 订单处理流程
```typescript
// 订单状态枚举
enum OrderStatus {
  PENDING = 'pending',           // 待付款
  PAID = 'paid',                // 已付款
  PROCESSING = 'processing',     // 处理中
  SHIPPED = 'shipped',          // 已发货
  DELIVERED = 'delivered',      // 已送达
  CANCELLED = 'cancelled',      // 已取消
  REFUNDED = 'refunded'         // 已退款
}

// 订单处理服务
class OrderService {
  // 创建订单
  async createOrder(orderData: CreateOrderData): Promise<Order> {
    // 1. 验证商品库存
    // 2. 计算总价
    // 3. 创建订单记录
    // 4. 扣减库存
    // 5. 发送确认邮件
  }
  
  // 更新订单状态
  async updateOrderStatus(orderId: string, status: OrderStatus): Promise<void> {
    // 1. 更新订单状态
    // 2. 记录状态变更日志
    // 3. 发送状态更新通知
    // 4. 处理库存变动（如取消订单时恢复库存）
  }
  
  // 处理退款
  async processRefund(orderId: string, amount?: number): Promise<void> {
    // 1. 验证退款条件
    // 2. 处理支付退款
    // 3. 恢复库存
    // 4. 更新订单状态
    // 5. 发送退款通知
  }
}
```

## 管理后台界面设计
```typescript
// 管理后台路由结构
const ADMIN_ROUTES = {
  dashboard: {
    path: '/admin/dashboard',
    component: 'AdminDashboard',
    features: ['sales_overview', 'recent_orders', 'inventory_alerts', 'analytics']
  },
  
  products: {
    path: '/admin/products',
    component: 'ProductManager',
    subRoutes: {
      list: '/admin/products',
      create: '/admin/products/create',
      edit: '/admin/products/[id]/edit',
      categories: '/admin/products/categories'
    }
  },
  
  orders: {
    path: '/admin/orders',
    component: 'OrderManager',
    features: ['order_list', 'order_details', 'status_management', 'payment_tracking']
  },
  
  analytics: {
    path: '/admin/analytics',
    component: 'Analytics',
    features: ['sales_reports', 'product_performance', 'customer_insights']
  }
};
```

## 数据库设计
```sql
-- 商品图片表
CREATE TABLE product_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  url VARCHAR(500) NOT NULL,
  alt_text VARCHAR(200),
  sort_order INTEGER DEFAULT 0,
  is_primary BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 商品变体表（尺寸、颜色等）
CREATE TABLE product_variants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  sku VARCHAR(100) UNIQUE,
  price DECIMAL(10,2),
  inventory INTEGER DEFAULT 0,
  attributes JSONB, -- 存储变体属性如颜色、尺寸等
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 订单项表
CREATE TABLE order_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id),
  product_variant_id UUID REFERENCES product_variants(id),
  quantity INTEGER NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 库存变动记录表
CREATE TABLE inventory_movements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id),
  product_variant_id UUID REFERENCES product_variants(id),
  movement_type VARCHAR(20) NOT NULL, -- 'in', 'out', 'adjustment'
  quantity INTEGER NOT NULL,
  reason VARCHAR(100),
  reference_id UUID, -- 关联订单ID或其他参考ID
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW()
);

-- 商品评价表
CREATE TABLE product_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id),
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  title VARCHAR(200),
  content TEXT,
  is_verified_purchase BOOLEAN DEFAULT false,
  is_approved BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## API接口设计
```typescript
// 商品管理API
const PRODUCT_API_ROUTES = {
  // 商品CRUD
  'GET /api/products': '获取商品列表',
  'GET /api/products/[id]': '获取商品详情',
  'POST /api/products': '创建商品',
  'PUT /api/products/[id]': '更新商品',
  'DELETE /api/products/[id]': '删除商品',
  
  // 分类管理
  'GET /api/products/categories': '获取分类列表',
  'POST /api/products/categories': '创建分类',
  'PUT /api/products/categories/[id]': '更新分类',
  
  // 库存管理
  'GET /api/products/[id]/inventory': '获取库存信息',
  'POST /api/products/[id]/inventory': '更新库存',
  'GET /api/products/low-stock': '获取低库存商品',
  
  // 订单管理
  'GET /api/orders': '获取订单列表',
  'GET /api/orders/[id]': '获取订单详情',
  'POST /api/orders': '创建订单',
  'PUT /api/orders/[id]/status': '更新订单状态',
  
  // 支付处理
  'POST /api/payment/create-order': '创建PayPal订单',
  'POST /api/payment/capture-order': '捕获PayPal支付'
};
```

## 组件架构
```typescript
// 商品系统模块结构
const PRODUCT_MODULE_STRUCTURE = {
  components: {
    'ProductCard.tsx': '商品卡片',
    'ProductGrid.tsx': '商品网格',
    'ProductDetail.tsx': '商品详情',
    'ProductFilters.tsx': '筛选器',
    'ProductForm.tsx': '商品表单',
    'ImageUpload.tsx': '图片上传'
  },
  
  admin: {
    'ProductManager.tsx': '商品管理主页',
    'ProductEditor.tsx': '商品编辑器',
    'CategoryManager.tsx': '分类管理',
    'InventoryManager.tsx': '库存管理',
    'OrderManager.tsx': '订单管理'
  },
  
  hooks: {
    'useProducts.ts': '商品数据',
    'useCart.ts': '购物车逻辑',
    'usePayment.ts': '支付处理',
    'useProductForm.ts': '商品表单逻辑',
    'useFileUpload.ts': '文件上传'
  },
  
  services: {
    'productService.ts': '商品服务',
    'categoryService.ts': '分类服务',
    'orderService.ts': '订单服务',
    'uploadService.ts': '上传服务'
  }
};
```

## 简化设计原则

### 保持简洁
- **核心功能优先**：专注于基本的商品管理需求
- **界面简洁**：避免过度复杂的功能
- **易于使用**：直观的操作流程

### 可扩展性
- **模块化设计**：功能模块独立，便于后续扩展
- **数据结构灵活**：支持未来添加新的商品属性
- **API标准化**：便于第三方集成

### SEO优化
- **商品页面SEO**：独立URL、结构化数据、多语言支持
- **分类页面优化**：清晰的分类导航和筛选功能
- **相关推荐**：基于分类和标签的相关商品推荐
