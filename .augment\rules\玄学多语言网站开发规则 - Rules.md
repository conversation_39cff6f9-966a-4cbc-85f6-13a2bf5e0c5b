---
type: "always_apply"
---

# 玄学多语言网站开发规则 - Cursor Rules

## 项目概述
构建一个专业的多语言玄学网站，包含塔罗、星座、星盘等东西方神秘学内容。采用内容驱动+商品驱动的SEO策略，最终发展为权威垂直网站并提供高质量外链服务。

## 技术栈架构

### 前端框架
- **Next.js 14 (App Router)** - SSR/SSG混合模式，SEO友好
- **TypeScript** - 严格类型检查，提升代码质量
- **Tailwind CSS** - 响应式设计，快速样式开发
- **Framer Motion** - 优雅的页面动画和过渡效果
- **React Hook Form** - 表单处理和验证
- **Zustand** - 轻量级状态管理

### 后端技术
- **Next.js API Routes** - 服务端逻辑处理
- **PostgreSQL** - 主数据库，支持复杂查询
- **Prisma ORM** - 数据库操作和类型安全
- **Redis** - 缓存和会话管理
- **CloudFlare** - CDN和安全防护

### 内容管理
- **Sanity CMS** - 无头CMS，多语言内容管理
- **MDX** - Markdown + React组件，灵活的内容格式
- **Sharp** - 图片优化和处理

### 部署和监控
- **Vercel** - 部署平台，边缘计算优化
- **Sentry** - 错误监控和性能分析
- **Umami** - 开源Web分析，隐私友好
- **Uptime Robot** - 网站监控

## 项目结构设计

```
mystical-website/
├── README.md
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── package.json
├── prisma/
│   ├── schema.prisma
│   ├── migrations/
│   └── seed.ts
├── public/
│   ├── images/
│   │   ├── tarot/
│   │   ├── astrology/
│   │   └── crystals/
│   ├── icons/
│   └── sitemap.xml
├── src/
│   ├── app/
│   │   ├── [locale]/
│   │   │   ├── layout.tsx
│   │   │   ├── page.tsx
│   │   │   ├── globals.css
│   │   │   ├── loading.tsx
│   │   │   ├── error.tsx
│   │   │   ├── not-found.tsx
│   │   │   ├── blog/                    # 博客模块
│   │   │   ├── products/                # 商品模块
│   │   │   ├── tarot/                   # 塔罗专题
│   │   │   ├── astrology/               # 星座专题
│   │   │   ├── natal-chart/             # 星盘专题
│   │   │   ├── numerology/              # 数字命理
│   │   │   ├── about/
│   │   │   ├── contact/
│   │   │   └── privacy/
│   │   ├── api/                         # API路由
│   │   └── middleware.ts
│   ├── components/
│   │   ├── ui/                          # 基础UI组件
│   │   ├── layout/                      # 布局组件
│   │   ├── seo/                         # SEO组件
│   │   ├── blog/                        # 博客组件
│   │   ├── products/                    # 商品组件
│   │   ├── mystical/                    # 玄学专用组件
│   │   ├── forms/                       # 表单组件
│   │   └── interactive/                 # 交互组件
│   ├── lib/                             # 工具库
│   ├── hooks/                           # 自定义Hooks
│   ├── stores/                          # 状态管理
│   ├── types/                           # TypeScript类型
│   ├── styles/                          # 样式文件
│   └── data/                            # 静态数据
├── messages/                            # 国际化文件
│   ├── en.json
│   ├── zh.json
│   ├── es.json
│   └── fr.json
└── docs/                               # 项目文档
    ├── api.md
    ├── deployment.md
    └── content-guidelines.md
```

## 核心开发原则

### 1. SEO至上原则
- 每个页面必须包含完整的SEO配置
- 使用Next.js的generateMetadata进行动态SEO
- 实现正确的标题层级结构 (h1 > h2 > h3)
- 包含结构化数据(JSON-LD)以获得富媒体摘要
- 优化核心网络指标(Core Web Vitals)
- 实现完整的站点地图和robots.txt

### 2. 性能优化原则
- 所有图片使用Next.js Image组件，配置适当的sizes属性
- 使用动态导入(dynamic import)分割代码
- 实现适当的缓存策略(ISR + Redis)
- 使用Suspense边界处理加载状态
- 预加载关键资源，延迟加载非关键内容

### 3. 多语言架构原则
- 使用next-intl进行国际化，支持en、zh、es、fr
- 实现SEO友好的URL结构：/[locale]/[category]/[slug]
- 正确配置hreflang标签
- 为每种语言提供独立的内容和SEO元数据
- 实现语言回退机制

### 4. 代码质量原则
- 严格的TypeScript配置，启用所有严格检查
- 使用ESLint + Prettier保持代码风格一致
- 实现完整的错误边界和错误处理
- 遵循组件单一职责原则
- 使用自定义Hooks抽象业务逻辑

## 设计系统规范

### 颜色系统
```javascript
// tailwind.config.js主题配置
theme: {
  extend: {
    colors: {
      mystical: {
        primary: { /* 神秘紫色系 50-900 */ },
        gold: { /* 黄金色系 50-900 */ },
        dark: { /* 深色系 50-900 */ },
        accent: { /* 强调色 */ }
      }
    },
    fontFamily: {
      sans: ['Inter', 'sans-serif'],
      serif: ['Playfair Display', 'serif'],
      mystical: ['Cinzel', 'serif'],
    },
    animation: {
      'mystical-glow': 'mysticalGlow 2s ease-in-out infinite alternate',
      'tarot-flip': 'tarotFlip 0.6s ease-in-out',
      'fade-in': 'fadeIn 0.5s ease-in-out',
    }
  }
}
```

### 组件样式规范
- 使用Tailwind CSS的变体系统定义组件状态
- 创建可复用的样式类组合
- 实现深色模式支持
- 保持设计系统的一致性

## 数据库设计

### 核心数据模型
```
User (用户)
├── 基础信息：id, email, name, avatar, role, locale
├── 关联：BlogPost[], Comment[], Order[]

BlogPost (博客文章)
├── 内容字段：title, content, excerpt, coverImage
├── SEO字段：slug, seoData, keywords
├── 分类标签：Category, Tag[]
├── 状态管理：isPublished, isFeatured, publishedAt
├── 多语言：locale
├── 统计数据：viewCount, Comment[]

Product (商品)
├── 基础信息：name, slug, description, price
├── 分类管理：Category, images[]
├── 库存管理：inventory, sku, isActive
├── 多语言：locale

Category (分类)
├── 层级结构：name, slug, description
├── 显示配置：color, icon
├── 多语言支持：locale

Order (订单) - 简化设计
├── 基本信息：orderNumber, status, total
├── 支付集成：paypalId
├── 关联：User, OrderItem[]
```

### 数据关系原则
- 使用适当的外键约束保证数据完整性
- 为查询频繁的字段创建数据库索引
- 实现软删除而非硬删除
- 使用JSON字段存储灵活的元数据

## 功能模块解耦设计

### 1. 博客系统模块
```
BlogModule/
├── components/
│   ├── BlogCard.tsx         # 文章卡片
│   ├── BlogList.tsx         # 文章列表
│   ├── BlogDetail.tsx       # 文章详情
│   ├── BlogSidebar.tsx      # 侧边栏
│   └── RelatedPosts.tsx     # 相关文章
├── hooks/
│   ├── useBlogPosts.ts      # 文章数据获取
│   ├── useBlogCategories.ts # 分类数据
│   └── useBlogSearch.ts     # 搜索功能
├── types/
│   └── blog.ts              # 博客相关类型
└── utils/
    ├── blogHelpers.ts       # 博客工具函数
    └── contentGenerator.ts  # 内容生成工具
```

### 2. 商品系统模块
```
ProductModule/
├── components/
│   ├── ProductCard.tsx      # 商品卡片
│   ├── ProductGrid.tsx      # 商品网格
│   ├── ProductDetail.tsx    # 商品详情
│   └── ProductFilters.tsx   # 筛选器
├── hooks/
│   ├── useProducts.ts       # 商品数据
│   ├── useCart.ts          # 购物车逻辑
│   └── usePayment.ts       # 支付处理
├── stores/
│   └── cartStore.ts        # 购物车状态
└── types/
    └── product.ts          # 商品相关类型
```

### 3. 玄学工具模块
```
MysticalModule/
├── components/
│   ├── TarotReader.tsx      # 塔罗牌占卜
│   ├── NatalChart.tsx       # 星盘计算
│   ├── NumerologyCalc.tsx   # 数字命理
│   └── CrystalGuide.tsx     # 水晶指南
├── data/
│   ├── tarotCards.ts        # 塔罗牌数据
│   ├── zodiacSigns.ts       # 星座数据
│   └── crystalData.ts       # 水晶数据
├── utils/
│   ├── tarotLogic.ts        # 塔罗逻辑
│   ├── astrologyCalc.ts     # 占星计算
│   └── numerologyCalc.ts    # 数字命理计算
└── types/
    └── mystical.ts          # 玄学相关类型
```

## API设计规范

### RESTful API结构
```
/api/
├── blog/
│   ├── route.ts            # GET /api/blog - 获取文章列表
│   ├── [slug]/route.ts     # GET /api/blog/[slug] - 获取文章详情
│   └── categories/route.ts # GET /api/blog/categories - 获取分类
├── products/
│   ├── route.ts            # GET /api/products - 获取商品列表
│   ├── [id]/route.ts       # GET /api/products/[id] - 获取商品详情
│   └── categories/route.ts # GET /api/products/categories - 获取分类
├── payment/
│   ├── create-order/route.ts    # POST - 创建PayPal订单
│   └── capture-order/route.ts   # POST - 捕获PayPal支付
├── newsletter/route.ts     # POST - 邮件订阅
├── contact/route.ts        # POST - 联系表单
├── search/route.ts         # GET - 全站搜索
└── sitemap/route.ts        # GET - 动态生成站点地图
```

### API响应格式标准
```typescript
// 成功响应格式
interface ApiResponse<T> {
  success: true;
  data: T;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 错误响应格式
interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}
```

## 缓存策略

### 多层缓存架构
1. **浏览器缓存** - 静态资源长期缓存
2. **CDN缓存** - CloudFlare边缘缓存
3. **Next.js缓存** - ISR增量静态再生成
4. **Redis缓存** - 数据库查询结果缓存
5. **数据库缓存** - PostgreSQL查询计划缓存

### 缓存策略配置
```typescript
// 缓存时间配置
const CACHE_TIMES = {
  STATIC_ASSETS: '1y',        // 静态资源
  BLOG_POSTS: '1h',           # 博客文章
  PRODUCT_DATA: '30m',        # 商品数据
  CATEGORIES: '6h',           # 分类数据
  USER_SESSION: '24h',        # 用户会话
  SEARCH_RESULTS: '15m',      # 搜索结果
};
```

## SEO优化策略

### 技术SEO
- 实现完整的XML站点地图
- 配置robots.txt优化爬虫抓取
- 使用结构化数据标记内容
- 优化页面加载速度和Core Web Vitals
- 实现面包屑导航

### 内容SEO
- 针对长尾关键词优化内容
- 建立内部链接网络
- 优化图片alt标签和文件名
- 创建主题聚类内容
- 定期更新和优化现有内容

### 多语言SEO
- 正确配置hreflang标签
- 为每种语言创建独立的站点地图
- 本地化URL结构和内容
- 避免内容重复和翻译问题

## 内容管理策略

### 内容分类体系
```
玄学内容分类/
├── 塔罗牌 (Tarot)
│   ├── 牌意解读
│   ├── 牌阵教程
│   ├── 实战案例
│   └── 新手指南
├── 占星学 (Astrology)  
│   ├── 星座解析
│   ├── 行星影响
│   ├── 宫位系统
│   └── 流年运势
├── 数字命理 (Numerology)
│   ├── 生命数字
│   ├── 姓名学
│   ├── 天使数字
│   └── 计算工具
└── 水晶疗愈 (Crystals)
    ├── 水晶属性
    ├── 脉轮对应
    ├── 净化方法
    └── 搭配指南
```

### 内容生产流程
1. **关键词研究** - 识别目标关键词和搜索意图
2. **内容规划** - 制定内容日历和主题计划
3. **内容创作** - 遵循SEO写作规范
4. **质量审核** - 内容质量和事实准确性检查
5. **SEO优化** - 元数据、内链、图片优化
6. **发布上线** - 多语言版本同步发布
7. **性能监控** - 跟踪排名和用户参与度

## 部署和监控

### 部署配置
- **Vercel部署** - 自动化部署和预览
- **环境变量管理** - 开发、测试、生产环境隔离
- **数据库迁移** - Prisma迁移管理
- **CDN配置** - 静态资源优化分发

### 监控指标
- **性能监控** - Core Web Vitals、页面加载时间
- **SEO监控** - 关键词排名、索引状态、流量数据
- **错误监控** - 应用错误、API异常、用户反馈
- **业务监控** - 用户行为、转化率、内容表现

## 安全和合规

### 安全措施
- HTTPS强制加密
- 输入验证和SQL注入防护
- CSRF和XSS攻击防护
- 环境变量安全管理
- 定期安全依赖更新

### 数据隐私
- GDPR合规性配置
- Cookie同意管理
- 用户数据最小化收集
- 数据删除和导出功能

## 开发工作流

### 代码质量控制
- 使用Husky实现Git Hooks
- pre-commit代码格式化和静态检查
- 代码审查机制
- 自动化测试集成

### 版本控制策略
- 功能分支开发模式
- 语义化版本控制
- 变更日志维护
- 回滚策略预案

记住：这个项目的核心目标是通过高质量内容建立SEO权威性，最终发展为可持续的外链服务业务。每个技术决策都应该服务于这个商业目标。