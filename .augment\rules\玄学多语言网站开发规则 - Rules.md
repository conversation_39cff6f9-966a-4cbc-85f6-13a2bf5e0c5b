---
type: "always_apply"
---

# 玄学多语言网站开发规则 - Cursor Rules

## 项目概述
构建一个专业的多语言玄学网站，包含塔罗、星座、星盘等东西方神秘学内容。采用内容驱动+商品驱动的SEO策略，最终发展为权威垂直网站并提供高质量外链服务。

## 技术栈架构

### 前端框架
- **Next.js 14 (App Router)** - SSR/SSG混合模式，SEO友好
- **TypeScript** - 严格类型检查，提升代码质量
- **Tailwind CSS** - 响应式设计，快速样式开发
- **Framer Motion** - 优雅的页面动画和过渡效果
- **React Hook Form** - 表单处理和验证
- **Zustand** - 轻量级状态管理

### 后端技术
- **Next.js API Routes** - 服务端逻辑处理
- **PostgreSQL** - 主数据库，支持复杂查询
- **Prisma ORM** - 数据库操作和类型安全
- **Redis** - 缓存和会话管理
- **CloudFlare** - CDN和安全防护

### AI集成技术
- **通义千问 (Qwen)** - 阿里云大语言模型，主要AI服务提供商
- **豆包 (Doubao)** - 字节跳动AI模型，备用服务
- **智谱AI (ZhipuAI)** - 国产AI模型，多语言支持
- **AI服务管理** - 智能路由、负载均衡、故障转移
- **提示词工程** - 专业的玄学领域提示词库

### 内容管理
- **Sanity CMS** - 无头CMS，多语言内容管理
- **MDX** - Markdown + React组件，灵活的内容格式
- **Sharp** - 图片优化和处理

### 部署和监控
- **Vercel** - 部署平台，边缘计算优化
- **Sentry** - 错误监控和性能分析
- **Umami** - 开源Web分析，隐私友好
- **Uptime Robot** - 网站监控

## 项目结构设计

```
mystical-website/
├── README.md
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── package.json
├── prisma/
│   ├── schema.prisma
│   ├── migrations/
│   └── seed.ts
├── public/
│   ├── images/
│   │   ├── tarot/
│   │   ├── astrology/
│   │   └── crystals/
│   ├── icons/
│   └── sitemap.xml
├── src/
│   ├── app/
│   │   ├── [locale]/
│   │   │   ├── layout.tsx
│   │   │   ├── page.tsx
│   │   │   ├── globals.css
│   │   │   ├── loading.tsx
│   │   │   ├── error.tsx
│   │   │   ├── not-found.tsx
│   │   │   ├── blog/                    # 博客模块
│   │   │   ├── products/                # 商品模块
│   │   │   ├── tarot/                   # 塔罗专题
│   │   │   ├── astrology/               # 星座专题
│   │   │   ├── natal-chart/             # 星盘专题
│   │   │   ├── numerology/              # 数字命理
│   │   │   ├── tests/                   # 在线测试中心
│   │   │   │   ├── tarot-test/          # 塔罗牌测试
│   │   │   │   ├── astrology-test/      # 星座测试
│   │   │   │   ├── numerology-test/     # 数字命理测试
│   │   │   │   ├── crystal-test/        # 水晶测试
│   │   │   │   ├── palmistry-test/      # 手相测试
│   │   │   │   └── dream-test/          # 梦境解析测试
│   │   │   ├── admin/                   # 管理后台
│   │   │   │   ├── dashboard/           # 仪表板
│   │   │   │   ├── products/            # 商品管理
│   │   │   │   ├── orders/              # 订单管理
│   │   │   │   ├── categories/          # 分类管理
│   │   │   │   └── analytics/           # 数据分析
│   │   │   ├── about/
│   │   │   ├── contact/
│   │   │   └── privacy/
│   │   ├── api/                         # API路由
│   │   └── middleware.ts
│   ├── components/
│   │   ├── ui/                          # 基础UI组件
│   │   ├── layout/                      # 布局组件
│   │   ├── seo/                         # SEO组件
│   │   ├── blog/                        # 博客组件
│   │   ├── products/                    # 商品组件
│   │   ├── mystical/                    # 玄学专用组件
│   │   ├── forms/                       # 表单组件
│   │   ├── interactive/                 # 交互组件
│   │   ├── tests/                       # 在线测试组件
│   │   └── ai/                          # AI集成组件
│   ├── lib/                             # 工具库
│   ├── hooks/                           # 自定义Hooks
│   ├── stores/                          # 状态管理
│   ├── types/                           # TypeScript类型
│   ├── styles/                          # 样式文件
│   └── data/                            # 静态数据
├── messages/                            # 国际化文件
│   ├── en.json                      # 英语（全球通用）
│   ├── zh.json                      # 中文（中国+华人市场）
│   ├── es.json                      # 西班牙语（西班牙+拉美）
│   ├── pt.json                      # 葡萄牙语（巴西+葡语区）
│   ├── hi.json                      # 印地语（印度核心市场）
│   ├── ja.json                      # 日语（日本高消费市场）
│   ├── de.json                      # 德语（德国+德语区）
│   ├── fr.json                      # 法语（法国+法语区）
│   ├── it.json                      # 意大利语（传统占星市场）
│   ├── ru.json                      # 俄语（俄罗斯+东欧）
│   ├── ko.json                      # 韩语（韩国新兴市场）
│   └── ar.json                      # 阿拉伯语（阿拉伯世界）
└── docs/                               # 项目文档
    ├── api.md
    ├── deployment.md
    └── content-guidelines.md
```

## 核心开发原则

### 1. SEO至上原则
- 每个页面必须包含完整的SEO配置
- 使用Next.js的generateMetadata进行动态SEO
- 实现正确的标题层级结构 (h1 > h2 > h3)
- 包含结构化数据(JSON-LD)以获得富媒体摘要
- 优化核心网络指标(Core Web Vitals)
- 实现完整的站点地图和robots.txt

### 2. 性能优化原则
- 所有图片使用Next.js Image组件，配置适当的sizes属性
- 使用动态导入(dynamic import)分割代码
- 实现适当的缓存策略(ISR + Redis)
- 使用Suspense边界处理加载状态
- 预加载关键资源，延迟加载非关键内容

### 3. 多语言架构原则
- 使用next-intl进行国际化，支持多语言扩展策略
- 实现SEO友好的URL结构：/[locale]/[category]/[slug]
- 正确配置hreflang标签
- 为每种语言提供独立的内容和SEO元数据
- 实现语言回退机制

#### 多语言扩展策略
```typescript
// 第一阶段：核心市场语言 (立即实施)
const PHASE_1_LANGUAGES = {
  en: 'English',     // 全球通用语，最大市场
  zh: '中文',         // 中国+华人市场 (14亿人口)
  es: 'Español',     // 西班牙+拉美市场 (5亿人口)
  pt: 'Português',   // 巴西+葡语区 (2.6亿人口)
  hi: 'हिन्दी',       // 印度北部核心市场 (6亿人口)
  ja: '日本語',       // 日本高消费市场 (1.25亿人口)
};

// 第二阶段：重要区域语言 (6个月后)
const PHASE_2_LANGUAGES = {
  de: 'Deutsch',     // 德国+德语区 (1亿人口)
  fr: 'Français',    // 法国+法语区 (2.8亿人口)
  it: 'Italiano',    // 意大利传统占星市场 (6500万人口)
  ru: 'Русский',     // 俄罗斯+东欧 (2.6亿人口)
  ko: '한국어',       // 韩国新兴市场 (5100万人口)
  ar: 'العربية',     // 阿拉伯世界 (4.2亿人口)
};

// 第三阶段：补充市场语言 (12个月后)
const PHASE_3_LANGUAGES = {
  th: 'ไทย',         // 泰国东南亚市场 (7000万人口)
  tr: 'Türkçe',      // 土耳其欧亚市场 (8400万人口)
  nl: 'Nederlands',  // 荷兰+比利时 (2800万人口)
  pl: 'Polski',      // 波兰东欧市场 (3800万人口)
  id: 'Bahasa',      // 印尼东南亚最大市场 (2.7亿人口)
};
```

#### 语言优先级决策因素
1. **市场规模** - 目标语言使用人口数量
2. **消费能力** - 目标市场的经济水平和消费意愿
3. **玄学接受度** - 当地对占星、塔罗等的文化接受程度
4. **竞争程度** - 现有玄学内容的竞争激烈程度
5. **SEO机会** - 搜索量大但竞争相对较小的关键词机会
6. **内容本地化成本** - 翻译和本地化的成本效益比

### 4. 代码质量原则
- 严格的TypeScript配置，启用所有严格检查
- 使用ESLint + Prettier保持代码风格一致
- 实现完整的错误边界和错误处理
- 遵循组件单一职责原则
- 使用自定义Hooks抽象业务逻辑

## 设计系统规范

### 整体设计理念
- **神秘优雅**：融合现代设计与神秘学美学
- **信任感建立**：专业、权威的视觉呈现
- **文化包容性**：适应不同文化背景的用户
- **情感共鸣**：营造神秘、温暖、智慧的氛围

### 颜色系统
```javascript
// tailwind.config.js主题配置
theme: {
  extend: {
    colors: {
      // 主色调 - 神秘紫色系
      mystical: {
        50: '#faf7ff',   // 极浅紫，背景色
        100: '#f3ecff',  // 浅紫，卡片背景
        200: '#e9d8ff',  // 淡紫，悬停状态
        300: '#d8b9ff',  // 中浅紫，边框
        400: '#c084fc',  // 中紫，次要按钮
        500: '#a855f7',  // 标准紫，主按钮
        600: '#9333ea',  // 深紫，按钮悬停
        700: '#7c3aed',  // 更深紫，激活状态
        800: '#6b21a8',  // 很深紫，文字
        900: '#581c87',  // 最深紫，标题
      },

      // 辅助色 - 黄金色系
      gold: {
        50: '#fffbeb',   // 极浅金
        100: '#fef3c7',  // 浅金
        200: '#fde68a',  // 淡金
        300: '#fcd34d',  // 中浅金
        400: '#fbbf24',  // 中金
        500: '#f59e0b',  // 标准金，强调色
        600: '#d97706',  // 深金
        700: '#b45309',  // 更深金
        800: '#92400e',  // 很深金
        900: '#78350f',  // 最深金
      },

      // 深色系 - 神秘黑色系
      dark: {
        50: '#f8fafc',   // 极浅灰
        100: '#f1f5f9',  // 浅灰
        200: '#e2e8f0',  // 淡灰
        300: '#cbd5e1',  // 中浅灰
        400: '#94a3b8',  // 中灰
        500: '#64748b',  // 标准灰
        600: '#475569',  // 深灰
        700: '#334155',  // 更深灰
        800: '#1e293b',  // 很深灰，深色模式背景
        900: '#0f172a',  // 最深灰，深色模式主背景
      },

      // 功能色系
      success: {
        light: '#d1fae5',
        DEFAULT: '#10b981',
        dark: '#047857',
      },
      warning: {
        light: '#fef3c7',
        DEFAULT: '#f59e0b',
        dark: '#d97706',
      },
      error: {
        light: '#fee2e2',
        DEFAULT: '#ef4444',
        dark: '#dc2626',
      },

      // 星座色彩系统
      zodiac: {
        fire: '#ff6b6b',      // 火象星座 - 红色系
        earth: '#51cf66',     // 土象星座 - 绿色系
        air: '#74c0fc',       // 风象星座 - 蓝色系
        water: '#845ef7',     // 水象星座 - 紫色系
      }
    },

    fontFamily: {
      // 主要字体 - 现代无衬线
      sans: ['Inter', 'Noto Sans', 'system-ui', 'sans-serif'],

      // 标题字体 - 优雅衬线
      serif: ['Playfair Display', 'Noto Serif', 'Georgia', 'serif'],

      // 神秘字体 - 装饰性
      mystical: ['Cinzel', 'Trajan Pro', 'serif'],

      // 等宽字体 - 代码/数据
      mono: ['JetBrains Mono', 'Fira Code', 'Consolas', 'monospace'],
    },

    fontSize: {
      // 扩展字体大小系统
      'xs': ['0.75rem', { lineHeight: '1rem' }],
      'sm': ['0.875rem', { lineHeight: '1.25rem' }],
      'base': ['1rem', { lineHeight: '1.5rem' }],
      'lg': ['1.125rem', { lineHeight: '1.75rem' }],
      'xl': ['1.25rem', { lineHeight: '1.75rem' }],
      '2xl': ['1.5rem', { lineHeight: '2rem' }],
      '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
      '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
      '5xl': ['3rem', { lineHeight: '1' }],
      '6xl': ['3.75rem', { lineHeight: '1' }],
      '7xl': ['4.5rem', { lineHeight: '1' }],
      '8xl': ['6rem', { lineHeight: '1' }],
      '9xl': ['8rem', { lineHeight: '1' }],
    },

    spacing: {
      // 扩展间距系统
      '18': '4.5rem',
      '88': '22rem',
      '128': '32rem',
    },

    animation: {
      // 神秘学主题动画
      'mystical-glow': 'mysticalGlow 3s ease-in-out infinite alternate',
      'tarot-flip': 'tarotFlip 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
      'crystal-shine': 'crystalShine 2s ease-in-out infinite',
      'star-twinkle': 'starTwinkle 1.5s ease-in-out infinite alternate',
      'fade-in': 'fadeIn 0.6s ease-out',
      'slide-up': 'slideUp 0.5s ease-out',
      'scale-in': 'scaleIn 0.3s ease-out',
      'float': 'float 3s ease-in-out infinite',
    },

    keyframes: {
      mysticalGlow: {
        '0%': {
          boxShadow: '0 0 20px rgba(168, 85, 247, 0.3)',
          transform: 'scale(1)'
        },
        '100%': {
          boxShadow: '0 0 40px rgba(168, 85, 247, 0.6)',
          transform: 'scale(1.02)'
        }
      },
      tarotFlip: {
        '0%': { transform: 'rotateY(0deg)' },
        '50%': { transform: 'rotateY(90deg)' },
        '100%': { transform: 'rotateY(0deg)' }
      },
      crystalShine: {
        '0%, 100%': { opacity: '0.8' },
        '50%': { opacity: '1', transform: 'scale(1.05)' }
      },
      starTwinkle: {
        '0%': { opacity: '0.5', transform: 'scale(0.8)' },
        '100%': { opacity: '1', transform: 'scale(1.2)' }
      },
      fadeIn: {
        '0%': { opacity: '0', transform: 'translateY(10px)' },
        '100%': { opacity: '1', transform: 'translateY(0)' }
      },
      slideUp: {
        '0%': { transform: 'translateY(100%)' },
        '100%': { transform: 'translateY(0)' }
      },
      scaleIn: {
        '0%': { transform: 'scale(0.9)', opacity: '0' },
        '100%': { transform: 'scale(1)', opacity: '1' }
      },
      float: {
        '0%, 100%': { transform: 'translateY(0px)' },
        '50%': { transform: 'translateY(-10px)' }
      }
    },

    backdropBlur: {
      xs: '2px',
    },

    boxShadow: {
      // 神秘学主题阴影
      'mystical': '0 10px 25px -3px rgba(168, 85, 247, 0.1), 0 4px 6px -2px rgba(168, 85, 247, 0.05)',
      'mystical-lg': '0 20px 40px -4px rgba(168, 85, 247, 0.15), 0 8px 16px -4px rgba(168, 85, 247, 0.1)',
      'gold': '0 10px 25px -3px rgba(245, 158, 11, 0.1), 0 4px 6px -2px rgba(245, 158, 11, 0.05)',
      'inner-mystical': 'inset 0 2px 4px 0 rgba(168, 85, 247, 0.1)',
    }
  }
}
```

### 组件样式规范
- **一致性原则**：所有组件遵循统一的设计语言
- **层次结构**：清晰的视觉层级和信息架构
- **交互反馈**：悬停、点击、加载状态的视觉反馈
- **响应式设计**：适配所有设备尺寸
- **深色模式**：完整的深色主题支持
- **可访问性**：符合WCAG 2.1 AA标准

### 页面布局设计规范

#### 整体布局结构
```typescript
// 页面布局层次
const LAYOUT_HIERARCHY = {
  // 主布局容器
  container: {
    maxWidth: '1440px',
    margin: '0 auto',
    padding: { mobile: '1rem', tablet: '1.5rem', desktop: '2rem' }
  },

  // 页面区域划分
  sections: {
    header: { height: '80px', sticky: true, zIndex: 50 },
    hero: { minHeight: '60vh', background: 'gradient' },
    content: { padding: '4rem 0', background: 'white/dark' },
    sidebar: { width: '320px', sticky: true },
    footer: { padding: '3rem 0', background: 'dark' }
  },

  // 网格系统
  grid: {
    columns: { mobile: 1, tablet: 2, desktop: 3, wide: 4 },
    gap: { mobile: '1rem', tablet: '1.5rem', desktop: '2rem' },
    breakpoints: {
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px'
    }
  }
};
```

#### 导航设计规范
```typescript
// 导航系统设计
const NAVIGATION_DESIGN = {
  // 主导航
  primary: {
    position: 'sticky-top',
    background: 'backdrop-blur + mystical-gradient',
    items: ['首页', '塔罗', '占星', '测试', '商品', '博客'],
    style: {
      height: '80px',
      fontSize: 'lg',
      fontWeight: 'medium',
      spacing: '2rem'
    }
  },

  // 移动端导航
  mobile: {
    type: 'hamburger-menu',
    animation: 'slide-from-right',
    overlay: 'backdrop-blur-lg',
    style: {
      fullScreen: true,
      background: 'mystical-900/95',
      textColor: 'white'
    }
  },

  // 面包屑导航
  breadcrumb: {
    position: 'below-header',
    style: {
      fontSize: 'sm',
      color: 'mystical-600',
      separator: '/',
      padding: '1rem 0'
    }
  },

  // 侧边栏导航
  sidebar: {
    categories: 'collapsible-tree',
    filters: 'accordion-style',
    style: {
      background: 'mystical-50',
      border: 'mystical-200',
      borderRadius: 'lg'
    }
  }
};
```

#### 卡片设计系统
```typescript
// 卡片组件设计规范
const CARD_DESIGN_SYSTEM = {
  // 基础卡片
  base: {
    background: 'white dark:dark-800',
    border: '1px solid mystical-200 dark:dark-700',
    borderRadius: 'xl',
    shadow: 'mystical',
    padding: '1.5rem',
    transition: 'all 0.3s ease'
  },

  // 卡片变体
  variants: {
    // 博客文章卡片
    blog: {
      image: { aspectRatio: '16:9', borderRadius: 'lg' },
      title: { fontSize: 'xl', fontWeight: 'bold', color: 'mystical-900' },
      excerpt: { fontSize: 'sm', color: 'mystical-600', lines: 3 },
      meta: { fontSize: 'xs', color: 'mystical-500' },
      hover: 'transform scale-105 shadow-mystical-lg'
    },

    // 商品卡片
    product: {
      image: { aspectRatio: '1:1', borderRadius: 'lg' },
      title: { fontSize: 'lg', fontWeight: 'semibold' },
      price: { fontSize: 'xl', fontWeight: 'bold', color: 'gold-600' },
      rating: { stars: 'gold-400', text: 'mystical-600' },
      button: { width: 'full', variant: 'mystical' }
    },

    // 测试结果卡片
    test: {
      background: 'gradient-to-br from-mystical-50 to-gold-50',
      icon: { size: '3rem', color: 'mystical-600' },
      title: { fontSize: '2xl', fontWeight: 'bold', textAlign: 'center' },
      description: { fontSize: 'base', textAlign: 'center', color: 'mystical-700' },
      animation: 'mystical-glow'
    },

    // 塔罗牌卡片
    tarot: {
      aspectRatio: '2:3',
      background: 'gradient-mystical',
      border: '2px solid gold-400',
      borderRadius: '2xl',
      shadow: 'mystical-lg',
      hover: 'animate-tarot-flip',
      backface: 'mystical-pattern'
    }
  }
};
```

### 交互设计规范

#### 按钮设计系统
```typescript
// 按钮组件设计
const BUTTON_DESIGN = {
  // 基础样式
  base: {
    fontWeight: 'medium',
    borderRadius: 'lg',
    transition: 'all 0.2s ease',
    focus: 'ring-2 ring-mystical-500 ring-offset-2'
  },

  // 尺寸变体
  sizes: {
    xs: { padding: '0.5rem 0.75rem', fontSize: 'xs' },
    sm: { padding: '0.75rem 1rem', fontSize: 'sm' },
    md: { padding: '1rem 1.5rem', fontSize: 'base' },
    lg: { padding: '1.25rem 2rem', fontSize: 'lg' },
    xl: { padding: '1.5rem 2.5rem', fontSize: 'xl' }
  },

  // 颜色变体
  variants: {
    primary: {
      background: 'mystical-600',
      color: 'white',
      hover: 'mystical-700',
      shadow: 'mystical'
    },
    secondary: {
      background: 'mystical-100',
      color: 'mystical-700',
      hover: 'mystical-200',
      border: 'mystical-300'
    },
    gold: {
      background: 'gold-500',
      color: 'white',
      hover: 'gold-600',
      shadow: 'gold'
    },
    ghost: {
      background: 'transparent',
      color: 'mystical-600',
      hover: 'mystical-50',
      border: 'mystical-300'
    },
    danger: {
      background: 'error-500',
      color: 'white',
      hover: 'error-600'
    }
  }
};
```

#### 表单设计规范
```typescript
// 表单组件设计
const FORM_DESIGN = {
  // 输入框样式
  input: {
    base: {
      background: 'white dark:dark-800',
      border: '2px solid mystical-200 dark:dark-600',
      borderRadius: 'lg',
      padding: '0.75rem 1rem',
      fontSize: 'base',
      transition: 'all 0.2s ease'
    },
    states: {
      focus: 'border-mystical-500 ring-2 ring-mystical-200',
      error: 'border-error-500 ring-2 ring-error-200',
      success: 'border-success-500 ring-2 ring-success-200',
      disabled: 'opacity-50 cursor-not-allowed'
    }
  },

  // 标签样式
  label: {
    fontSize: 'sm',
    fontWeight: 'medium',
    color: 'mystical-700 dark:mystical-300',
    marginBottom: '0.5rem'
  },

  // 错误信息
  error: {
    fontSize: 'sm',
    color: 'error-600',
    marginTop: '0.25rem',
    icon: 'exclamation-circle'
  },

  // 帮助文本
  help: {
    fontSize: 'sm',
    color: 'mystical-500',
    marginTop: '0.25rem'
  }
};
```

### 响应式设计规范

#### 断点系统
```typescript
// 响应式断点配置
const RESPONSIVE_BREAKPOINTS = {
  mobile: {
    max: '767px',
    container: '100%',
    padding: '1rem',
    columns: 1,
    navigation: 'hamburger'
  },
  tablet: {
    min: '768px',
    max: '1023px',
    container: '768px',
    padding: '1.5rem',
    columns: 2,
    navigation: 'horizontal'
  },
  desktop: {
    min: '1024px',
    max: '1279px',
    container: '1024px',
    padding: '2rem',
    columns: 3,
    navigation: 'horizontal-with-dropdown'
  },
  wide: {
    min: '1280px',
    container: '1280px',
    padding: '2rem',
    columns: 4,
    navigation: 'full-horizontal'
  }
};
```

#### 移动端优化
```typescript
// 移动端特殊设计
const MOBILE_OPTIMIZATIONS = {
  // 触摸友好
  touch: {
    minTouchTarget: '44px',
    buttonSpacing: '0.5rem',
    swipeGestures: true,
    pullToRefresh: true
  },

  // 性能优化
  performance: {
    lazyLoading: true,
    imageOptimization: true,
    criticalCSS: true,
    prefetching: 'viewport'
  },

  // 移动端特有功能
  features: {
    bottomNavigation: true,
    floatingActionButton: true,
    swipeableCards: true,
    infiniteScroll: true
  }
};
```

### 主题化设计规范

#### 深色模式设计
```typescript
// 深色模式配色方案
const DARK_MODE_THEME = {
  // 背景色系
  backgrounds: {
    primary: 'dark-900',      // 主背景
    secondary: 'dark-800',    // 卡片背景
    tertiary: 'dark-700',     // 悬停背景
    overlay: 'dark-900/80'    // 遮罩背景
  },

  // 文字色系
  text: {
    primary: 'white',         // 主要文字
    secondary: 'dark-200',    // 次要文字
    tertiary: 'dark-400',     // 辅助文字
    accent: 'mystical-400'    // 强调文字
  },

  // 边框色系
  borders: {
    primary: 'dark-600',      // 主要边框
    secondary: 'dark-700',    // 次要边框
    accent: 'mystical-600'    // 强调边框
  },

  // 切换动画
  transition: {
    duration: '0.3s',
    easing: 'ease-in-out',
    properties: ['background-color', 'border-color', 'color']
  }
};
```

#### 文化主题适配
```typescript
// 不同文化的主题适配
const CULTURAL_THEMES = {
  // 中文主题 - 东方神秘
  chinese: {
    colors: {
      primary: 'red-600',      // 中国红
      secondary: 'gold-500',   // 金色
      accent: 'emerald-600'    // 翡翠绿
    },
    fonts: {
      primary: 'Noto Sans SC',
      decorative: 'Ma Shan Zheng'
    },
    patterns: 'chinese-clouds',
    symbols: ['yin-yang', 'dragon', 'phoenix']
  },

  // 印度主题 - 吠陀风格
  indian: {
    colors: {
      primary: 'orange-600',   // 橙色
      secondary: 'purple-600', // 紫色
      accent: 'yellow-500'     // 黄色
    },
    fonts: {
      primary: 'Noto Sans Devanagari',
      decorative: 'Kalam'
    },
    patterns: 'mandala',
    symbols: ['om', 'lotus', 'chakra']
  },

  // 阿拉伯主题 - 伊斯兰风格
  arabic: {
    colors: {
      primary: 'blue-600',     // 蓝色
      secondary: 'green-600',  // 绿色
      accent: 'gold-500'       // 金色
    },
    fonts: {
      primary: 'Noto Sans Arabic',
      decorative: 'Amiri'
    },
    patterns: 'geometric',
    symbols: ['crescent', 'star', 'geometric-patterns']
  }
};
```

### 动效设计规范

#### 页面过渡动画
```typescript
// 页面切换动画
const PAGE_TRANSITIONS = {
  // 路由切换
  route: {
    enter: 'fade-in + slide-up',
    exit: 'fade-out + slide-down',
    duration: '0.5s',
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
  },

  // 模态框动画
  modal: {
    backdrop: 'fade-in',
    content: 'scale-in + fade-in',
    duration: '0.3s'
  },

  // 侧边栏动画
  sidebar: {
    mobile: 'slide-from-left',
    desktop: 'fade-in',
    duration: '0.4s'
  }
};
```

#### 微交互动画
```typescript
// 微交互设计
const MICRO_INTERACTIONS = {
  // 按钮交互
  button: {
    hover: 'scale-105 + shadow-lg',
    active: 'scale-95',
    loading: 'pulse + disabled'
  },

  // 卡片交互
  card: {
    hover: 'lift + glow',
    click: 'scale-98',
    loading: 'skeleton-animation'
  },

  // 表单交互
  form: {
    focus: 'border-glow + scale-102',
    error: 'shake + red-glow',
    success: 'green-glow + checkmark'
  },

  // 塔罗牌特殊动画
  tarot: {
    flip: 'rotate-y-180 + mystical-glow',
    shuffle: 'random-rotation + float',
    reveal: 'scale-in + golden-glow'
  }
};
```

### 图标和插图规范

#### 图标系统
```typescript
// 图标使用规范
const ICON_SYSTEM = {
  // 图标库选择
  library: 'Heroicons + Lucide + Custom Mystical Icons',

  // 尺寸规范
  sizes: {
    xs: '16px',    // 内联图标
    sm: '20px',    // 按钮图标
    md: '24px',    // 导航图标
    lg: '32px',    // 标题图标
    xl: '48px',    // 特色图标
    '2xl': '64px'  // 装饰图标
  },

  // 颜色规范
  colors: {
    primary: 'mystical-600',
    secondary: 'mystical-400',
    accent: 'gold-500',
    muted: 'mystical-300',
    inverse: 'white'
  },

  // 神秘学专用图标
  mystical: {
    tarot: ['tarot-card', 'crystal-ball', 'magic-wand'],
    astrology: ['zodiac-signs', 'planet', 'constellation'],
    numerology: ['numbers', 'calculator', 'infinity'],
    crystals: ['gem', 'crystal', 'healing-stone'],
    general: ['moon-phases', 'pentagram', 'eye', 'hand']
  }
};
```

#### 插图风格指南
```typescript
// 插图设计规范
const ILLUSTRATION_GUIDE = {
  // 风格定义
  style: {
    type: 'modern-mystical',
    complexity: 'medium',
    colorPalette: 'mystical + gold',
    strokeWidth: '2px',
    corners: 'rounded'
  },

  // 使用场景
  usage: {
    hero: 'large-scale-illustration',
    empty: 'simple-line-art',
    error: 'friendly-character',
    loading: 'animated-symbols'
  },

  // 主题插图
  themes: {
    tarot: 'mystical-cards + celestial-elements',
    astrology: 'zodiac-wheel + star-maps',
    crystals: 'geometric-gems + energy-flows',
    meditation: 'peaceful-figures + nature-elements'
  }
};
```

### 内容展示规范

#### 文章排版
```typescript
// 文章内容排版规范
const ARTICLE_TYPOGRAPHY = {
  // 标题层级
  headings: {
    h1: { fontSize: '3xl', fontWeight: 'bold', color: 'mystical-900', marginBottom: '2rem' },
    h2: { fontSize: '2xl', fontWeight: 'semibold', color: 'mystical-800', marginBottom: '1.5rem' },
    h3: { fontSize: 'xl', fontWeight: 'medium', color: 'mystical-700', marginBottom: '1rem' },
    h4: { fontSize: 'lg', fontWeight: 'medium', color: 'mystical-600', marginBottom: '0.75rem' }
  },

  // 段落样式
  paragraph: {
    fontSize: 'base',
    lineHeight: '1.7',
    color: 'mystical-700',
    marginBottom: '1.5rem'
  },

  // 引用样式
  blockquote: {
    borderLeft: '4px solid mystical-400',
    paddingLeft: '1.5rem',
    fontStyle: 'italic',
    color: 'mystical-600',
    background: 'mystical-50',
    padding: '1rem 1.5rem',
    borderRadius: 'lg'
  },

  // 列表样式
  list: {
    marginBottom: '1.5rem',
    paddingLeft: '1.5rem',
    bullet: 'mystical-400',
    spacing: '0.5rem'
  }
};
```

#### 媒体展示
```typescript
// 媒体内容展示规范
const MEDIA_DISPLAY = {
  // 图片展示
  images: {
    aspectRatios: {
      hero: '21:9',
      card: '16:9',
      square: '1:1',
      portrait: '3:4',
      tarot: '2:3'
    },
    loading: 'skeleton + blur-placeholder',
    error: 'fallback-illustration',
    optimization: 'webp + lazy-loading'
  },

  // 视频展示
  videos: {
    player: 'custom-styled',
    controls: 'mystical-theme',
    poster: 'high-quality-thumbnail',
    loading: 'mystical-spinner'
  },

  // 画廊展示
  gallery: {
    layout: 'masonry + lightbox',
    navigation: 'thumbnail + arrows',
    zoom: 'smooth-zoom',
    sharing: 'social-media-integration'
  }
};
```

## 数据库设计

### 核心数据模型
```
User (用户)
├── 基础信息：id, email, name, avatar, role, locale
├── 关联：BlogPost[], Comment[], Order[]

BlogPost (博客文章)
├── 内容字段：title, content, excerpt, coverImage
├── SEO字段：slug, seoData, keywords
├── 分类标签：Category, Tag[]
├── 状态管理：isPublished, isFeatured, publishedAt
├── 多语言：locale
├── 统计数据：viewCount, Comment[]

Product (商品)
├── 基础信息：name, slug, description, price
├── 分类管理：Category, images[]
├── 库存管理：inventory, sku, isActive
├── 多语言：locale

Category (分类)
├── 层级结构：name, slug, description
├── 显示配置：color, icon
├── 多语言支持：locale

Order (订单) - 简化设计
├── 基本信息：orderNumber, status, total
├── 支付集成：paypalId
├── 关联：User, OrderItem[]
```

### 数据关系原则
- 使用适当的外键约束保证数据完整性
- 为查询频繁的字段创建数据库索引
- 实现软删除而非硬删除
- 使用JSON字段存储灵活的元数据

## 功能模块解耦设计

### 1. 博客系统模块
```
BlogModule/
├── components/
│   ├── BlogCard.tsx         # 文章卡片
│   ├── BlogList.tsx         # 文章列表
│   ├── BlogDetail.tsx       # 文章详情
│   ├── BlogSidebar.tsx      # 侧边栏
│   └── RelatedPosts.tsx     # 相关文章
├── hooks/
│   ├── useBlogPosts.ts      # 文章数据获取
│   ├── useBlogCategories.ts # 分类数据
│   └── useBlogSearch.ts     # 搜索功能
├── types/
│   └── blog.ts              # 博客相关类型
└── utils/
    ├── blogHelpers.ts       # 博客工具函数
    └── contentGenerator.ts  # 内容生成工具
```

### 2. 商品系统模块
```
ProductModule/
├── components/
│   ├── ProductCard.tsx      # 商品卡片
│   ├── ProductGrid.tsx      # 商品网格
│   ├── ProductDetail.tsx    # 商品详情
│   ├── ProductFilters.tsx   # 筛选器
│   ├── ProductForm.tsx      # 商品表单
│   ├── ProductList.tsx      # 商品列表
│   ├── ProductStatus.tsx    # 商品状态
│   └── ImageUpload.tsx      # 图片上传
├── admin/
│   ├── ProductManager.tsx   # 商品管理主页
│   ├── ProductEditor.tsx    # 商品编辑器
│   ├── CategoryManager.tsx  # 分类管理
│   ├── InventoryManager.tsx # 库存管理
│   └── OrderManager.tsx     # 订单管理
├── hooks/
│   ├── useProducts.ts       # 商品数据
│   ├── useCart.ts          # 购物车逻辑
│   ├── usePayment.ts       # 支付处理
│   ├── useProductForm.ts   # 商品表单逻辑
│   └── useFileUpload.ts    # 文件上传
├── stores/
│   ├── cartStore.ts        # 购物车状态
│   └── adminStore.ts       # 管理员状态
├── services/
│   ├── productService.ts   # 商品服务
│   ├── categoryService.ts  # 分类服务
│   ├── orderService.ts     # 订单服务
│   └── uploadService.ts    # 上传服务
└── types/
    ├── product.ts          # 商品相关类型
    ├── category.ts         # 分类类型
    └── order.ts            # 订单类型
```

### 3. 玄学工具模块
```
MysticalModule/
├── components/
│   ├── TarotReader.tsx      # 塔罗牌占卜
│   ├── NatalChart.tsx       # 星盘计算
│   ├── NumerologyCalc.tsx   # 数字命理
│   └── CrystalGuide.tsx     # 水晶指南
├── data/
│   ├── tarotCards.ts        # 塔罗牌数据
│   ├── zodiacSigns.ts       # 星座数据
│   └── crystalData.ts       # 水晶数据
├── utils/
│   ├── tarotLogic.ts        # 塔罗逻辑
│   ├── astrologyCalc.ts     # 占星计算
│   └── numerologyCalc.ts    # 数字命理计算
└── types/
    └── mystical.ts          # 玄学相关类型
```

### 4. 在线测试系统模块
```
TestModule/
├── components/
│   ├── TestContainer.tsx    # 测试容器组件
│   ├── QuestionCard.tsx     # 问题卡片
│   ├── ProgressBar.tsx      # 进度条
│   ├── ResultDisplay.tsx    # 结果展示
│   ├── ShareResult.tsx      # 结果分享
│   └── TestHistory.tsx      # 测试历史
├── tests/
│   ├── TarotTest.tsx        # 塔罗牌测试
│   ├── AstrologyTest.tsx    # 星座测试
│   ├── NumerologyTest.tsx   # 数字命理测试
│   ├── CrystalTest.tsx      # 水晶测试
│   ├── PalmistryTest.tsx    # 手相测试
│   └── DreamTest.tsx        # 梦境解析测试
├── hooks/
│   ├── useTestFlow.ts       # 测试流程管理
│   ├── useAIAnalysis.ts     # AI分析Hook
│   ├── useTestResults.ts    # 测试结果管理
│   └── useTestHistory.ts    # 测试历史
├── services/
│   ├── aiService.ts         # AI服务集成
│   ├── testService.ts       # 测试数据服务
│   └── analyticsService.ts  # 分析服务
├── types/
│   ├── test.ts              # 测试相关类型
│   ├── ai.ts                # AI相关类型
│   └── result.ts            # 结果相关类型
└── data/
    ├── testQuestions.ts     # 测试问题库
    ├── promptTemplates.ts   # AI提示词模板
    └── testConfigs.ts       # 测试配置
```

### 5. AI服务集成模块
```
AIModule/
├── providers/
│   ├── QwenProvider.ts      # 通义千问服务
│   ├── DoubaoProvider.ts    # 豆包服务
│   ├── ZhipuProvider.ts     # 智谱AI服务
│   └── AIRouter.ts          # AI服务路由
├── services/
│   ├── AIService.ts         # 统一AI服务接口
│   ├── PromptEngine.ts      # 提示词引擎
│   ├── ResponseParser.ts    # 响应解析器
│   └── CacheManager.ts      # AI响应缓存
├── prompts/
│   ├── tarotPrompts.ts      # 塔罗牌提示词
│   ├── astrologyPrompts.ts  # 占星提示词
│   ├── numerologyPrompts.ts # 数字命理提示词
│   ├── crystalPrompts.ts    # 水晶提示词
│   ├── palmistryPrompts.ts  # 手相提示词
│   └── dreamPrompts.ts      # 梦境解析提示词
├── types/
│   ├── aiProvider.ts        # AI提供商类型
│   ├── prompt.ts            # 提示词类型
│   └── response.ts          # 响应类型
└── utils/
    ├── promptBuilder.ts     # 提示词构建器
    ├── responseValidator.ts # 响应验证器
    └── errorHandler.ts      # 错误处理
```

## API设计规范

### RESTful API结构
```
/api/
├── blog/
│   ├── route.ts            # GET /api/blog - 获取文章列表
│   ├── [slug]/route.ts     # GET /api/blog/[slug] - 获取文章详情
│   └── categories/route.ts # GET /api/blog/categories - 获取分类
├── products/
│   ├── route.ts            # GET /api/products - 获取商品列表
│   ├── [id]/route.ts       # GET /api/products/[id] - 获取商品详情
│   └── categories/route.ts # GET /api/products/categories - 获取分类
├── payment/
│   ├── create-order/route.ts    # POST - 创建PayPal订单
│   └── capture-order/route.ts   # POST - 捕获PayPal支付
├── tests/                  # 在线测试API
│   ├── route.ts            # GET /api/tests - 获取测试列表
│   ├── [testType]/
│   │   ├── route.ts        # GET /api/tests/[testType] - 获取测试配置
│   │   ├── questions/route.ts # GET - 获取测试问题
│   │   ├── submit/route.ts    # POST - 提交测试答案
│   │   └── result/[id]/route.ts # GET - 获取测试结果
│   └── history/route.ts    # GET - 获取用户测试历史
├── ai/                     # AI服务API
│   ├── analyze/route.ts    # POST - AI分析请求
│   ├── generate/route.ts   # POST - AI内容生成
│   └── health/route.ts     # GET - AI服务健康检查
├── newsletter/route.ts     # POST - 邮件订阅
├── contact/route.ts        # POST - 联系表单
├── search/route.ts         # GET - 全站搜索
└── sitemap/route.ts        # GET - 动态生成站点地图
```

### API响应格式标准
```typescript
// 成功响应格式
interface ApiResponse<T> {
  success: true;
  data: T;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 错误响应格式
interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}
```

## 在线测试功能设计

### 测试类型和功能规划
```typescript
// 测试类型配置
const TEST_TYPES = {
  tarot: {
    name: '塔罗牌测试',
    description: '通过选择塔罗牌获得人生指导',
    duration: '5-10分钟',
    questions: 8,
    aiAnalysis: true,
    shareEnabled: true
  },
  astrology: {
    name: '星座性格测试',
    description: '深度分析你的星座特质和运势',
    duration: '3-5分钟',
    questions: 12,
    aiAnalysis: true,
    shareEnabled: true
  },
  numerology: {
    name: '数字命理测试',
    description: '通过生日和姓名解读生命密码',
    duration: '2-3分钟',
    questions: 6,
    aiAnalysis: true,
    shareEnabled: true
  },
  crystal: {
    name: '水晶能量测试',
    description: '找到最适合你的水晶和能量石',
    duration: '4-6分钟',
    questions: 10,
    aiAnalysis: true,
    shareEnabled: true
  },
  palmistry: {
    name: '手相解读测试',
    description: '通过手相特征解读性格和命运',
    duration: '6-8分钟',
    questions: 15,
    aiAnalysis: true,
    shareEnabled: true
  },
  dream: {
    name: '梦境解析测试',
    description: '解读梦境符号和潜意识信息',
    duration: '5-7分钟',
    questions: 8,
    aiAnalysis: true,
    shareEnabled: true
  }
};
```

### AI服务集成架构
```typescript
// AI服务提供商配置
const AI_PROVIDERS = {
  qwen: {
    name: '通义千问',
    endpoint: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
    model: 'qwen-turbo',
    priority: 1,
    rateLimit: 100, // 每分钟请求数
    timeout: 30000,
    features: ['text-generation', 'conversation']
  },
  doubao: {
    name: '豆包',
    endpoint: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
    model: 'ep-********-xxxxx',
    priority: 2,
    rateLimit: 80,
    timeout: 25000,
    features: ['text-generation', 'conversation']
  },
  zhipu: {
    name: '智谱AI',
    endpoint: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
    model: 'glm-4',
    priority: 3,
    rateLimit: 60,
    timeout: 20000,
    features: ['text-generation', 'conversation', 'multilingual']
  }
};

// AI服务路由策略
const AI_ROUTING_STRATEGY = {
  primary: 'qwen',           // 主要服务
  fallback: ['doubao', 'zhipu'], // 备用服务
  loadBalancing: 'round-robin',   // 负载均衡策略
  healthCheck: true,              // 健康检查
  retryAttempts: 3,              // 重试次数
  circuitBreaker: true           // 熔断器
};
```

### 测试流程设计
```typescript
// 测试流程状态机
const TEST_FLOW_STATES = {
  INIT: 'init',           // 初始化
  STARTED: 'started',     // 开始测试
  IN_PROGRESS: 'in_progress', // 进行中
  ANALYZING: 'analyzing', // AI分析中
  COMPLETED: 'completed', // 完成
  ERROR: 'error'          // 错误
};

// 测试数据结构
interface TestSession {
  id: string;
  userId?: string;
  testType: string;
  locale: string;
  state: TestFlowState;
  answers: TestAnswer[];
  result?: TestResult;
  aiAnalysis?: AIAnalysisResult;
  createdAt: Date;
  completedAt?: Date;
  shareToken?: string;
}

interface TestAnswer {
  questionId: string;
  answer: string | string[] | number;
  timestamp: Date;
}

interface TestResult {
  id: string;
  testSessionId: string;
  summary: string;
  details: ResultDetail[];
  recommendations: string[];
  aiInsights: string;
  shareableContent: ShareableContent;
}
```

### AI提示词工程
```typescript
// 提示词模板系统
const PROMPT_TEMPLATES = {
  tarot: {
    system: `你是一位专业的塔罗牌解读师，具有深厚的神秘学知识。
    请根据用户选择的塔罗牌和问题，提供准确、有洞察力的解读。
    解读应该包含：
    1. 牌面含义解释
    2. 当前情况分析
    3. 未来趋势预测
    4. 具体建议和指导

    请用温暖、专业的语调，避免过于绝对的预言。`,

    user: `用户选择了以下塔罗牌：{cards}
    用户的问题类型：{questionType}
    用户的具体情况：{userContext}

    请提供详细的塔罗牌解读。`
  },

  astrology: {
    system: `你是一位专业的占星师，精通西方占星学和心理占星学。
    请根据用户的星座信息和测试答案，提供个性化的性格分析和运势指导。
    分析应该包含：
    1. 性格特质分析
    2. 优势和潜力
    3. 需要注意的方面
    4. 近期运势趋势
    5. 人际关系建议

    请保持客观和建设性的态度。`,

    user: `用户的星座信息：{zodiacInfo}
    测试答案：{testAnswers}
    用户关注的领域：{focusAreas}

    请提供个性化的占星分析。`
  },

  numerology: {
    system: `你是一位数字命理学专家，精通生命数字、表达数字等各种数字命理系统。
    请根据用户的生日、姓名等信息，计算并解读相关的生命数字。
    解读应该包含：
    1. 生命数字含义
    2. 性格特质分析
    3. 人生使命和目标
    4. 幸运数字和颜色
    5. 发展建议

    请提供准确的计算和深入的解读。`,

    user: `用户信息：
    生日：{birthDate}
    姓名：{name}
    测试答案：{answers}

    请计算并解读用户的数字命理。`
  }
};
```

### 数据库设计扩展
```sql
-- 测试配置表
CREATE TABLE test_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  test_type VARCHAR(50) NOT NULL,
  locale VARCHAR(10) NOT NULL,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  questions JSONB NOT NULL,
  settings JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 测试会话表
CREATE TABLE test_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  test_type VARCHAR(50) NOT NULL,
  locale VARCHAR(10) NOT NULL,
  state VARCHAR(20) DEFAULT 'init',
  answers JSONB,
  metadata JSONB,
  share_token VARCHAR(100) UNIQUE,
  created_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP
);

-- 测试结果表
CREATE TABLE test_results (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  test_session_id UUID REFERENCES test_sessions(id),
  summary TEXT,
  details JSONB,
  ai_analysis TEXT,
  recommendations JSONB,
  shareable_content JSONB,
  view_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);

-- AI服务日志表
CREATE TABLE ai_service_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider VARCHAR(50) NOT NULL,
  test_session_id UUID REFERENCES test_sessions(id),
  prompt_tokens INTEGER,
  completion_tokens INTEGER,
  total_tokens INTEGER,
  response_time INTEGER,
  status VARCHAR(20),
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 商品管理扩展表结构
-- 商品图片表
CREATE TABLE product_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  url VARCHAR(500) NOT NULL,
  alt_text VARCHAR(200),
  sort_order INTEGER DEFAULT 0,
  is_primary BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 商品变体表（尺寸、颜色等）
CREATE TABLE product_variants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  sku VARCHAR(100) UNIQUE,
  price DECIMAL(10,2),
  inventory INTEGER DEFAULT 0,
  attributes JSONB, -- 存储变体属性如颜色、尺寸等
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 订单项表
CREATE TABLE order_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id),
  product_variant_id UUID REFERENCES product_variants(id),
  quantity INTEGER NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 库存变动记录表
CREATE TABLE inventory_movements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id),
  product_variant_id UUID REFERENCES product_variants(id),
  movement_type VARCHAR(20) NOT NULL, -- 'in', 'out', 'adjustment'
  quantity INTEGER NOT NULL,
  reason VARCHAR(100),
  reference_id UUID, -- 关联订单ID或其他参考ID
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW()
);

-- 商品评价表
CREATE TABLE product_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id),
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  title VARCHAR(200),
  content TEXT,
  is_verified_purchase BOOLEAN DEFAULT false,
  is_approved BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## 商品管理系统设计

### 商品管理功能规划
```typescript
// 商品管理核心功能
const PRODUCT_MANAGEMENT_FEATURES = {
  // 商品基础管理
  product_crud: {
    create: '创建新商品',
    read: '查看商品详情',
    update: '编辑商品信息',
    delete: '删除商品（软删除）',
    bulk_operations: '批量操作'
  },

  // 库存管理
  inventory: {
    stock_tracking: '库存跟踪',
    low_stock_alerts: '低库存提醒',
    inventory_movements: '库存变动记录',
    bulk_update: '批量库存更新'
  },

  // 分类管理
  categories: {
    category_tree: '分类树管理',
    drag_drop_sort: '拖拽排序',
    multi_level: '多级分类',
    seo_optimization: 'SEO优化'
  },

  // 订单管理
  orders: {
    order_list: '订单列表',
    order_details: '订单详情',
    status_update: '状态更新',
    payment_tracking: '支付跟踪'
  },

  // 图片管理
  media: {
    image_upload: '图片上传',
    image_optimization: '图片优化',
    gallery_management: '图片库管理',
    bulk_upload: '批量上传'
  }
};
```

### 商品类型配置
```typescript
// 玄学商品分类
const PRODUCT_CATEGORIES = {
  tarot: {
    name: '塔罗牌',
    subcategories: [
      'classic-tarot', // 经典塔罗
      'oracle-cards', // 神谕卡
      'tarot-sets', // 塔罗套装
      'tarot-books' // 塔罗书籍
    ],
    attributes: ['deck_size', 'language', 'artist', 'publisher']
  },

  crystals: {
    name: '水晶饰品',
    subcategories: [
      'raw-crystals', // 原石
      'jewelry', // 首饰
      'home-decor', // 家居装饰
      'healing-sets' // 疗愈套装
    ],
    attributes: ['crystal_type', 'size', 'color', 'chakra']
  },

  books: {
    name: '玄学书籍',
    subcategories: [
      'astrology-books', // 占星书籍
      'tarot-guides', // 塔罗指南
      'numerology', // 数字命理
      'spirituality' // 灵性成长
    ],
    attributes: ['author', 'language', 'pages', 'publisher']
  },

  accessories: {
    name: '占星配件',
    subcategories: [
      'altar-tools', // 祭坛工具
      'incense', // 香薰
      'candles', // 蜡烛
      'meditation' // 冥想用品
    ],
    attributes: ['material', 'size', 'scent', 'color']
  }
};
```

### 管理后台界面设计
```typescript
// 管理后台路由结构
const ADMIN_ROUTES = {
  dashboard: {
    path: '/admin/dashboard',
    component: 'AdminDashboard',
    features: ['sales_overview', 'recent_orders', 'inventory_alerts', 'analytics']
  },

  products: {
    path: '/admin/products',
    component: 'ProductManager',
    subRoutes: {
      list: '/admin/products',
      create: '/admin/products/create',
      edit: '/admin/products/[id]/edit',
      categories: '/admin/products/categories'
    }
  },

  orders: {
    path: '/admin/orders',
    component: 'OrderManager',
    features: ['order_list', 'order_details', 'status_management', 'payment_tracking']
  },

  analytics: {
    path: '/admin/analytics',
    component: 'Analytics',
    features: ['sales_reports', 'product_performance', 'customer_insights']
  }
};
```

### 商品表单设计
```typescript
// 商品创建/编辑表单结构
interface ProductFormData {
  // 基础信息
  name: string;
  slug: string;
  description: string;
  shortDescription?: string;

  // 价格和库存
  price: number;
  compareAtPrice?: number;
  sku: string;
  inventory: number;
  trackInventory: boolean;

  // 分类和标签
  categoryId: string;
  tags: string[];

  // 图片
  images: ProductImage[];

  // SEO
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];

  // 状态
  isActive: boolean;
  isFeatured: boolean;

  // 多语言
  locale: string;
  translations?: Record<string, Partial<ProductFormData>>;

  // 商品变体（可选）
  variants?: ProductVariant[];

  // 自定义属性
  attributes?: Record<string, any>;
}

// 商品变体结构
interface ProductVariant {
  id?: string;
  name: string;
  sku: string;
  price?: number;
  inventory: number;
  attributes: Record<string, string>; // 如：{ color: 'red', size: 'M' }
  isActive: boolean;
}
```

### 库存管理逻辑
```typescript
// 库存管理服务
class InventoryService {
  // 库存检查
  async checkStock(productId: string, variantId?: string): Promise<number> {
    // 检查商品或变体的当前库存
  }

  // 库存扣减
  async reduceStock(
    productId: string,
    quantity: number,
    reason: string,
    variantId?: string
  ): Promise<void> {
    // 扣减库存并记录变动
  }

  // 库存增加
  async addStock(
    productId: string,
    quantity: number,
    reason: string,
    variantId?: string
  ): Promise<void> {
    // 增加库存并记录变动
  }

  // 低库存提醒
  async getLowStockProducts(threshold: number = 10): Promise<Product[]> {
    // 获取低库存商品列表
  }

  // 库存变动历史
  async getInventoryHistory(
    productId: string,
    variantId?: string
  ): Promise<InventoryMovement[]> {
    // 获取库存变动历史
  }
}
```

### 订单处理流程
```typescript
// 订单状态枚举
enum OrderStatus {
  PENDING = 'pending',           // 待付款
  PAID = 'paid',                // 已付款
  PROCESSING = 'processing',     // 处理中
  SHIPPED = 'shipped',          // 已发货
  DELIVERED = 'delivered',      // 已送达
  CANCELLED = 'cancelled',      // 已取消
  REFUNDED = 'refunded'         // 已退款
}

// 订单处理服务
class OrderService {
  // 创建订单
  async createOrder(orderData: CreateOrderData): Promise<Order> {
    // 1. 验证商品库存
    // 2. 计算总价
    // 3. 创建订单记录
    // 4. 扣减库存
    // 5. 发送确认邮件
  }

  // 更新订单状态
  async updateOrderStatus(orderId: string, status: OrderStatus): Promise<void> {
    // 1. 更新订单状态
    // 2. 记录状态变更日志
    // 3. 发送状态更新通知
    // 4. 处理库存变动（如取消订单时恢复库存）
  }

  // 处理退款
  async processRefund(orderId: string, amount?: number): Promise<void> {
    // 1. 验证退款条件
    // 2. 处理支付退款
    // 3. 恢复库存
    // 4. 更新订单状态
    // 5. 发送退款通知
  }
}
```

## 缓存策略

### 多层缓存架构
1. **浏览器缓存** - 静态资源长期缓存
2. **CDN缓存** - CloudFlare边缘缓存
3. **Next.js缓存** - ISR增量静态再生成
4. **Redis缓存** - 数据库查询结果缓存
5. **AI响应缓存** - AI分析结果缓存
6. **数据库缓存** - PostgreSQL查询计划缓存

### 缓存策略配置
```typescript
// 缓存时间配置
const CACHE_TIMES = {
  STATIC_ASSETS: '1y',        // 静态资源
  BLOG_POSTS: '1h',           # 博客文章
  PRODUCT_DATA: '30m',        # 商品数据
  CATEGORIES: '6h',           # 分类数据
  USER_SESSION: '24h',        # 用户会话
  SEARCH_RESULTS: '15m',      # 搜索结果
  TEST_CONFIGS: '2h',         # 测试配置
  AI_RESPONSES: '7d',         # AI响应结果
  TEST_RESULTS: '30d',        # 测试结果
};
```

## SEO优化策略

### 技术SEO
- 实现完整的XML站点地图
- 配置robots.txt优化爬虫抓取
- 使用结构化数据标记内容
- 优化页面加载速度和Core Web Vitals
- 实现面包屑导航

### 内容SEO
- 针对长尾关键词优化内容
- 建立内部链接网络
- 优化图片alt标签和文件名
- 创建主题聚类内容
- 定期更新和优化现有内容

### 多语言SEO
- 正确配置hreflang标签
- 为每种语言创建独立的站点地图
- 本地化URL结构和内容
- 避免内容重复和翻译问题

## 内容管理策略

### 内容分类体系
```
玄学内容分类/
├── 塔罗牌 (Tarot)
│   ├── 牌意解读
│   ├── 牌阵教程
│   ├── 实战案例
│   └── 新手指南
├── 占星学 (Astrology)
│   ├── 星座解析
│   ├── 行星影响
│   ├── 宫位系统
│   └── 流年运势
├── 数字命理 (Numerology)
│   ├── 生命数字
│   ├── 姓名学
│   ├── 天使数字
│   └── 计算工具
├── 水晶疗愈 (Crystals)
│   ├── 水晶属性
│   ├── 脉轮对应
│   ├── 净化方法
│   └── 搭配指南
├── 风水学 (Feng Shui)
│   ├── 家居风水
│   ├── 办公风水
│   ├── 商业风水
│   └── 风水工具
├── 手相学 (Palmistry)
│   ├── 生命线解读
│   ├── 智慧线分析
│   ├── 感情线解析
│   └── 手相基础
├── 梦境解析 (Dream Analysis)
│   ├── 梦境符号
│   ├── 梦境类型
│   ├── 解梦技巧
│   └── 梦境日记
└── 冥想灵修 (Meditation & Spirituality)
    ├── 冥想技巧
    ├── 脉轮平衡
    ├── 能量疗愈
    └── 灵性成长
```

### 多语言内容本地化策略

#### 文化适应性内容规划
```typescript
// 不同市场的内容重点
const MARKET_CONTENT_FOCUS = {
  // 印度市场 - 吠陀传统
  hi: {
    primary: ['vedic-astrology', 'yoga-philosophy', 'chakra-healing'],
    cultural: ['hindu-festivals', 'sanskrit-mantras', 'ayurveda'],
    keywords: ['ज्योतिष', 'वेद', 'चक्र', 'योग']
  },

  // 巴西市场 - 多元融合
  pt: {
    primary: ['tarot', 'crystals', 'shamanism'],
    cultural: ['umbanda', 'candomble', 'brazilian-spirituality'],
    keywords: ['tarô', 'cristais', 'espiritualidade', 'xamanismo']
  },

  // 日本市场 - 禅宗结合
  ja: {
    primary: ['astrology', 'meditation', 'feng-shui'],
    cultural: ['shinto-spirituality', 'zen-philosophy', 'japanese-divination'],
    keywords: ['占星術', '風水', '瞑想', '神道']
  },

  // 德国市场 - 理性神秘学
  de: {
    primary: ['astrology', 'numerology', 'psychology'],
    cultural: ['german-mysticism', 'anthroposophy', 'jung-psychology'],
    keywords: ['Astrologie', 'Numerologie', 'Mystik', 'Spiritualität']
  },

  // 意大利市场 - 文艺复兴传统
  it: {
    primary: ['astrology', 'tarot', 'renaissance-mysticism'],
    cultural: ['italian-witchcraft', 'catholic-mysticism', 'renaissance-magic'],
    keywords: ['astrologia', 'tarocchi', 'misticismo', 'spiritualità']
  },

  // 俄罗斯市场 - 东正教神秘学
  ru: {
    primary: ['astrology', 'numerology', 'slavic-mysticism'],
    cultural: ['orthodox-mysticism', 'slavic-paganism', 'russian-folklore'],
    keywords: ['астрология', 'нумерология', 'мистика', 'духовность']
  },

  // 阿拉伯市场 - 伊斯兰神秘学
  ar: {
    primary: ['islamic-astrology', 'numerology', 'sufi-mysticism'],
    cultural: ['sufi-traditions', 'islamic-spirituality', 'arabic-divination'],
    keywords: ['علم التنجيم', 'الروحانية', 'التصوف', 'الأرقام']
  },

  // 韩国市场 - 现代融合
  ko: {
    primary: ['astrology', 'tarot', 'korean-shamanism'],
    cultural: ['korean-fortune-telling', 'buddhist-mysticism', 'confucian-spirituality'],
    keywords: ['점성술', '타로', '무속', '영성']
  }
};
```

#### SEO关键词本地化策略
```typescript
// 各语言市场的SEO关键词策略
const SEO_KEYWORD_STRATEGY = {
  // 高搜索量 + 低竞争度关键词
  primary_keywords: {
    en: ['daily horoscope', 'tarot reading', 'astrology chart'],
    zh: ['每日星座运势', '塔罗牌占卜', '星盘分析'],
    es: ['horóscopo diario', 'lectura de tarot', 'carta astral'],
    pt: ['horóscopo diário', 'leitura de tarô', 'mapa astral'],
    hi: ['दैनिक राशिफल', 'टैरो रीडिंग', 'ज्योतिष चार्ट'],
    ja: ['今日の運勢', 'タロット占い', '星座占い'],
    de: ['tageshoroskop', 'tarot karten', 'astrologie'],
    fr: ['horoscope quotidien', 'tirage tarot', 'thème astral'],
    it: ['oroscopo giornaliero', 'lettura tarocchi', 'tema natale'],
    ru: ['ежедневный гороскоп', 'гадание таро', 'астрология'],
    ko: ['오늘의 운세', '타로점', '별자리운세'],
    ar: ['الأبراج اليومية', 'قراءة التاروت', 'علم التنجيم']
  },

  // 长尾关键词策略
  long_tail_keywords: {
    en: ['how to read tarot cards for beginners', 'what does my birth chart mean'],
    zh: ['初学者如何学塔罗牌', '出生星盘怎么看'],
    pt: ['como ler cartas de tarô para iniciantes', 'o que significa meu mapa astral'],
    hi: ['शुरुआती लोगों के लिए टैरो कार्ड कैसे पढ़ें', 'मेरे जन्म चार्ट का क्या मतलब है'],
    ja: ['初心者向けタロットカードの読み方', '出生図の意味とは'],
    de: ['tarot karten lesen für anfänger', 'was bedeutet mein geburtshoroskop']
  }
};
```

### 内容生产流程
1. **关键词研究** - 识别目标关键词和搜索意图
2. **内容规划** - 制定内容日历和主题计划
3. **内容创作** - 遵循SEO写作规范
4. **质量审核** - 内容质量和事实准确性检查
5. **SEO优化** - 元数据、内链、图片优化
6. **发布上线** - 多语言版本同步发布
7. **性能监控** - 跟踪排名和用户参与度

### 内容本地化质量标准

#### 翻译质量要求
- **专业术语准确性** - 玄学术语必须使用当地标准译名
- **文化适应性** - 内容需要符合当地文化背景和宗教敏感性
- **SEO友好性** - 翻译后的内容需要针对当地搜索习惯优化
- **可读性** - 保持原文的可读性和吸引力

#### 文化敏感性检查清单
- [ ] 是否符合当地宗教文化？
- [ ] 是否避免了文化禁忌？
- [ ] 是否使用了当地习惯的表达方式？
- [ ] 是否考虑了当地的节日和传统？
- [ ] 是否适应了当地的阅读习惯？

#### 本地化内容策略
```typescript
// 内容本地化配置
const LOCALIZATION_CONFIG = {
  // 印度市场本地化
  hi: {
    festivals: ['diwali', 'holi', 'navratri'],
    traditions: ['vedic-astrology', 'ayurveda', 'yoga'],
    taboos: ['beef-references', 'disrespect-to-gods'],
    preferred_content: ['spiritual-growth', 'karma-dharma', 'meditation']
  },

  // 巴西市场本地化
  pt: {
    festivals: ['carnival', 'festa-junina', 'christmas'],
    traditions: ['umbanda', 'candomble', 'spiritism'],
    taboos: ['religious-intolerance', 'cultural-appropriation'],
    preferred_content: ['energy-healing', 'spirit-guides', 'protection-rituals']
  },

  // 日本市场本地化
  ja: {
    festivals: ['new-year', 'obon', 'cherry-blossom'],
    traditions: ['shinto', 'buddhism', 'zen'],
    taboos: ['direct-confrontation', 'superstition-mockery'],
    preferred_content: ['harmony', 'balance', 'mindfulness']
  },

  // 阿拉伯市场本地化
  ar: {
    festivals: ['ramadan', 'eid', 'mawlid'],
    traditions: ['islamic-spirituality', 'sufi-mysticism'],
    taboos: ['shirk', 'haram-practices', 'religious-mixing'],
    preferred_content: ['islamic-astrology', 'spiritual-purification', 'divine-guidance']
  }
};
```

## 部署和监控

### 部署配置
- **Vercel部署** - 自动化部署和预览
- **环境变量管理** - 开发、测试、生产环境隔离
- **数据库迁移** - Prisma迁移管理
- **CDN配置** - 静态资源优化分发

### 监控指标
- **性能监控** - Core Web Vitals、页面加载时间
- **SEO监控** - 关键词排名、索引状态、流量数据
- **错误监控** - 应用错误、API异常、用户反馈
- **业务监控** - 用户行为、转化率、内容表现

## 安全和合规

### 安全措施
- HTTPS强制加密
- 输入验证和SQL注入防护
- CSRF和XSS攻击防护
- 环境变量安全管理
- 定期安全依赖更新

### 数据隐私
- GDPR合规性配置
- Cookie同意管理
- 用户数据最小化收集
- 数据删除和导出功能

## 商业化策略

### 收入模式设计
```typescript
// 多元化收入流
const REVENUE_STREAMS = {
  // 内容变现
  content: {
    premium_articles: '付费深度解读文章',
    subscription: '会员订阅服务',
    courses: '在线玄学课程',
    ebooks: '电子书销售'
  },

  // 商品销售
  products: {
    tarot_decks: '塔罗牌套装',
    crystals: '水晶饰品',
    books: '玄学书籍',
    accessories: '占星配件'
  },

  // 服务收费
  services: {
    personal_reading: '个人占卜服务',
    chart_analysis: '星盘分析服务',
    consultation: '玄学咨询',
    custom_content: '定制内容创作'
  },

  // 外链服务（终极目标）
  seo_services: {
    guest_posts: '客座文章发布',
    backlinks: '高质量外链',
    content_marketing: '内容营销服务',
    domain_authority: 'DA权重提升服务'
  }
};
```

### SEO权威性建设路径
```typescript
// 权威性建设阶段
const AUTHORITY_BUILDING_PHASES = {
  // 第一阶段：内容建设 (0-6个月)
  phase_1: {
    goal: '建立内容基础，获得初始流量',
    targets: ['500+优质文章', '10万+月访问量', 'DA 20+'],
    strategies: ['长尾关键词', '内容聚类', '内链建设']
  },

  // 第二阶段：权威确立 (6-18个月)
  phase_2: {
    goal: '成为垂直领域权威，获得行业认可',
    targets: ['1000+文章', '50万+月访问量', 'DA 40+'],
    strategies: ['专家合作', '媒体报道', '行业引用']
  },

  // 第三阶段：商业变现 (18个月+)
  phase_3: {
    goal: '提供外链服务，实现商业目标',
    targets: ['DA 60+', '100万+月访问量', '行业领导地位'],
    strategies: ['外链服务', '内容营销', 'B2B合作']
  }
};
```

### 外链服务产品设计
```typescript
// 外链服务包装
const BACKLINK_SERVICES = {
  // 基础外链包
  basic: {
    price: '$299/月',
    includes: ['2篇客座文章', 'DA 40+网站', '自然锚文本'],
    target_clients: '小型企业、个人博客'
  },

  // 专业外链包
  professional: {
    price: '$799/月',
    includes: ['5篇客座文章', 'DA 60+网站', '多样化锚文本', '社交推广'],
    target_clients: '中型企业、电商网站'
  },

  // 企业外链包
  enterprise: {
    price: '$1999/月',
    includes: ['10篇客座文章', 'DA 70+网站', '定制内容', '竞争对手分析'],
    target_clients: '大型企业、跨国公司'
  }
};
```

## 技术架构优化

### 性能优化策略
```typescript
// 性能优化配置
const PERFORMANCE_CONFIG = {
  // Core Web Vitals目标
  core_web_vitals: {
    LCP: '<2.5s',    // 最大内容绘制
    FID: '<100ms',   // 首次输入延迟
    CLS: '<0.1',     // 累积布局偏移
    TTFB: '<600ms'   // 首字节时间
  },

  // 缓存策略
  caching: {
    static_assets: '1年',
    blog_posts: '1小时',
    api_responses: '15分钟',
    user_sessions: '24小时'
  },

  // 图片优化
  image_optimization: {
    formats: ['webp', 'avif', 'jpg'],
    sizes: [320, 640, 768, 1024, 1280, 1920],
    quality: 85,
    lazy_loading: true
  }
};
```

### 国际化技术实现
```typescript
// 国际化配置
const I18N_CONFIG = {
  // 语言检测
  detection: {
    order: ['path', 'header', 'cookie', 'localStorage'],
    caches: ['cookie', 'localStorage']
  },

  // 回退策略
  fallback: {
    default: 'en',
    regional: {
      'zh-TW': 'zh',
      'pt-PT': 'pt',
      'es-MX': 'es'
    }
  },

  // SEO配置
  seo: {
    hreflang: true,
    canonical: true,
    alternate_urls: true,
    localized_sitemaps: true
  }
};
```

## 开发工作流

### 代码质量控制
- 使用Husky实现Git Hooks
- pre-commit代码格式化和静态检查
- 代码审查机制
- 自动化测试集成

### 版本控制策略
- 功能分支开发模式
- 语义化版本控制
- 变更日志维护
- 回滚策略预案

### 持续集成/持续部署
```yaml
# CI/CD流程配置
ci_cd_pipeline:
  stages:
    - lint_and_format
    - type_check
    - unit_tests
    - integration_tests
    - build
    - deploy_preview
    - lighthouse_audit
    - deploy_production

  quality_gates:
    - test_coverage: '>90%'
    - lighthouse_score: '>90'
    - bundle_size: '<500KB'
    - accessibility: 'AA级别'
```

## 组件架构设计原则

### 1. 组件分层架构
```
组件层级结构/
├── Atoms (原子组件)
│   ├── Button, Input, Icon, Badge
│   ├── Typography (Text, Heading, Caption)
│   ├── Loading (Spinner, Skeleton, Progress)
│   └── Media (Image, Video, Avatar)
├── Molecules (分子组件)
│   ├── SearchBox, Pagination, Breadcrumb
│   ├── Card, Modal, Tooltip, Dropdown
│   ├── FormField, FilterGroup, TagList
│   └── Navigation (NavItem, TabItem)
├── Organisms (有机体组件)
│   ├── Header, Footer, Sidebar
│   ├── ProductGrid, BlogList, CommentSection
│   ├── ContactForm, NewsletterForm
│   └── MysticalTools (TarotSpread, ChartWheel)
└── Templates (模板组件)
    ├── PageLayout, ContentLayout
    ├── BlogTemplate, ProductTemplate
    └── LandingTemplate, ErrorTemplate
```

### 2. 组件设计模式

#### 复合组件模式 (Compound Components)
```typescript
// 示例：Card组件系统
const Card = {
  Root: CardRoot,
  Header: CardHeader,
  Body: CardBody,
  Footer: CardFooter,
  Image: CardImage,
  Actions: CardActions
};

// 使用方式
<Card.Root>
  <Card.Header>
    <Card.Image src="..." alt="..." />
  </Card.Header>
  <Card.Body>
    <h3>Title</h3>
    <p>Content</p>
  </Card.Body>
  <Card.Footer>
    <Card.Actions>
      <Button>Action</Button>
    </Card.Actions>
  </Card.Footer>
</Card.Root>
```

#### 渲染属性模式 (Render Props)
```typescript
// 数据获取组件
interface DataFetcherProps<T> {
  url: string;
  children: (data: {
    data: T | null;
    loading: boolean;
    error: Error | null;
    refetch: () => void;
  }) => React.ReactNode;
}

// 使用方式
<DataFetcher url="/api/blog">
  {({ data, loading, error }) => (
    loading ? <Skeleton /> :
    error ? <ErrorMessage /> :
    <BlogList posts={data} />
  )}
</DataFetcher>
```

#### 高阶组件模式 (HOC)
```typescript
// SEO增强HOC
const withSEO = <P extends object>(
  Component: React.ComponentType<P>,
  defaultSEO: SEOConfig
) => {
  return (props: P & { seo?: Partial<SEOConfig> }) => {
    const seoConfig = { ...defaultSEO, ...props.seo };
    return (
      <>
        <SEOHead {...seoConfig} />
        <Component {...props} />
      </>
    );
  };
};

// 多语言增强HOC
const withI18n = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return (props: P) => {
    const { t, locale } = useTranslation();
    return <Component {...props} t={t} locale={locale} />;
  };
};
```

### 3. 通用组件接口设计

#### 基础组件Props接口
```typescript
// 所有组件的基础接口
interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  testId?: string;
  'aria-label'?: string;
}

// 可交互组件接口
interface InteractiveProps extends BaseComponentProps {
  disabled?: boolean;
  loading?: boolean;
  onClick?: (event: React.MouseEvent) => void;
}

// 表单组件接口
interface FormComponentProps extends BaseComponentProps {
  name: string;
  value?: any;
  onChange?: (value: any) => void;
  onBlur?: () => void;
  error?: string;
  required?: boolean;
}

// 数据展示组件接口
interface DataDisplayProps<T> extends BaseComponentProps {
  data: T[];
  loading?: boolean;
  error?: Error | null;
  emptyState?: React.ReactNode;
  renderItem?: (item: T, index: number) => React.ReactNode;
}
```

#### 主题和样式系统接口
```typescript
// 主题变体系统
type ComponentVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error';
type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

interface StyledComponentProps extends BaseComponentProps {
  variant?: ComponentVariant;
  size?: ComponentSize;
  fullWidth?: boolean;
  rounded?: boolean;
}

// 响应式属性系统
interface ResponsiveProps {
  xs?: any;
  sm?: any;
  md?: any;
  lg?: any;
  xl?: any;
}
```

### 4. 状态管理解耦策略

#### 全局状态分片管理
```typescript
// 使用Zustand创建模块化store
interface AppState {
  // UI状态
  ui: UIState;
  // 用户状态
  user: UserState;
  // 购物车状态
  cart: CartState;
  // 博客状态
  blog: BlogState;
  // 玄学工具状态
  mystical: MysticalState;
}

// 状态切片示例
const useUIStore = create<UIState>((set, get) => ({
  theme: 'light',
  sidebarOpen: false,
  modal: null,
  toggleTheme: () => set(state => ({
    theme: state.theme === 'light' ? 'dark' : 'light'
  })),
  openModal: (modal) => set({ modal }),
  closeModal: () => set({ modal: null }),
}));
```

#### 组件级状态隔离
```typescript
// 使用自定义Hook封装组件状态逻辑
const useProductFilters = (initialFilters: ProductFilters) => {
  const [filters, setFilters] = useState(initialFilters);
  const [appliedFilters, setAppliedFilters] = useState(initialFilters);

  const updateFilter = useCallback((key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const applyFilters = useCallback(() => {
    setAppliedFilters(filters);
  }, [filters]);

  const resetFilters = useCallback(() => {
    setFilters(initialFilters);
    setAppliedFilters(initialFilters);
  }, [initialFilters]);

  return {
    filters,
    appliedFilters,
    updateFilter,
    applyFilters,
    resetFilters,
    hasChanges: !isEqual(filters, appliedFilters)
  };
};
```

### 5. 数据层抽象设计

#### 通用数据服务接口
```typescript
// 基础CRUD服务接口
interface BaseService<T, CreateDTO, UpdateDTO> {
  getAll(params?: QueryParams): Promise<PaginatedResponse<T>>;
  getById(id: string): Promise<T>;
  create(data: CreateDTO): Promise<T>;
  update(id: string, data: UpdateDTO): Promise<T>;
  delete(id: string): Promise<void>;
  search(query: string, filters?: SearchFilters): Promise<PaginatedResponse<T>>;
}

// 缓存服务接口
interface CacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(pattern?: string): Promise<void>;
}

// 文件服务接口
interface FileService {
  upload(file: File, path: string): Promise<string>;
  delete(url: string): Promise<void>;
  getSignedUrl(path: string): Promise<string>;
  optimizeImage(url: string, options: ImageOptions): Promise<string>;
}
```

#### 数据获取Hook标准化
```typescript
// 通用数据获取Hook
const useQuery = <T>(
  key: string | string[],
  fetcher: () => Promise<T>,
  options?: {
    enabled?: boolean;
    refetchOnWindowFocus?: boolean;
    staleTime?: number;
    cacheTime?: number;
    onSuccess?: (data: T) => void;
    onError?: (error: Error) => void;
  }
) => {
  // 实现统一的数据获取逻辑
  // 包含loading、error、data状态管理
  // 自动缓存和重新验证
  // 错误重试机制
};

// 分页数据Hook
const usePaginatedQuery = <T>(
  key: string,
  fetcher: (page: number, limit: number) => Promise<PaginatedResponse<T>>,
  options?: PaginationOptions
) => {
  // 分页逻辑封装
  // 无限滚动支持
  // 页面状态管理
};
```

### 6. 错误处理和边界设计

#### 错误边界组件
```typescript
// 通用错误边界
interface ErrorBoundaryProps {
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'page' | 'section' | 'component';
}

// 异步错误边界
const AsyncErrorBoundary: React.FC<{
  children: React.ReactNode;
  fallback: React.ComponentType<AsyncErrorProps>;
}> = ({ children, fallback: Fallback }) => {
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      setError(new Error(event.reason));
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  if (error) {
    return <Fallback error={error} retry={() => setError(null)} />;
  }

  return <>{children}</>;
};
```

### 7. 测试策略和组件验证

#### 组件测试标准
```typescript
// 组件测试模板
describe('ComponentName', () => {
  // 渲染测试
  it('should render correctly', () => {
    render(<ComponentName {...defaultProps} />);
    expect(screen.getByRole('...')).toBeInTheDocument();
  });

  // 交互测试
  it('should handle user interactions', async () => {
    const onAction = jest.fn();
    render(<ComponentName {...defaultProps} onAction={onAction} />);

    await user.click(screen.getByRole('button'));
    expect(onAction).toHaveBeenCalledWith(expectedArgs);
  });

  // 可访问性测试
  it('should be accessible', async () => {
    const { container } = render(<ComponentName {...defaultProps} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  // 响应式测试
  it('should be responsive', () => {
    // 测试不同屏幕尺寸下的表现
  });
});
```

#### 视觉回归测试
```typescript
// Storybook + Chromatic集成
export default {
  title: 'Components/Button',
  component: Button,
  parameters: {
    chromatic: {
      viewports: [320, 768, 1200],
      delay: 300
    }
  }
};

// 视觉测试用例
export const AllVariants = () => (
  <div className="space-y-4">
    {variants.map(variant => (
      <Button key={variant} variant={variant}>
        {variant} Button
      </Button>
    ))}
  </div>
);
```

### 8. 性能优化组件模式

#### 虚拟化组件
```typescript
// 大列表虚拟化
const VirtualizedList = <T,>({
  items,
  renderItem,
  itemHeight,
  containerHeight
}: VirtualizedListProps<T>) => {
  // 使用react-window或自实现虚拟滚动
  // 只渲染可见区域的项目
  // 支持动态高度和水平滚动
};
```

#### 懒加载组件
```typescript
// 图片懒加载
const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  placeholder,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useIntersectionObserver(imgRef, {
    onIntersect: () => setIsInView(true),
    threshold: 0.1
  });

  return (
    <div ref={imgRef} {...props}>
      {isInView && (
        <img
          src={src}
          alt={alt}
          onLoad={() => setIsLoaded(true)}
          style={{ opacity: isLoaded ? 1 : 0 }}
        />
      )}
      {!isLoaded && placeholder}
    </div>
  );
};
```

记住：这个项目的核心目标是通过高质量内容建立SEO权威性，最终发展为可持续的外链服务业务。每个技术决策都应该服务于这个商业目标。

## 组件开发最佳实践

### 1. 组件命名和文件组织
- 使用PascalCase命名组件文件和组件名
- 组件文件夹包含：index.ts、Component.tsx、Component.test.tsx、Component.stories.tsx
- 使用描述性的组件名，避免缩写
- 按功能域而非技术层级组织组件

### 2. 组件文档和类型安全
- 每个组件必须包含完整的TypeScript类型定义
- 使用JSDoc注释描述组件用途和Props
- 提供Storybook故事展示所有组件变体
- 导出组件的Props类型供其他组件使用

### 3. 组件复用性检查清单
- [ ] 组件是否有单一职责？
- [ ] Props接口是否足够灵活？
- [ ] 是否支持自定义样式和类名？
- [ ] 是否处理了所有必要的可访问性属性？
- [ ] 是否支持响应式设计？
- [ ] 是否有适当的默认值？
- [ ] 是否可以在不同上下文中使用？