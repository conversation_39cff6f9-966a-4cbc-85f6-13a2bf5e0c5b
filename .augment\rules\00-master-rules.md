---
type: "always_apply"
---

# 玄学多语言网站开发 - 主规则文件

## 项目概述
构建一个专业的多语言玄学网站，包含塔罗、星座、星盘等东西方神秘学内容。采用内容驱动+商品驱动的SEO策略，最终发展为权威垂直网站并提供高质量外链服务。

## 核心开发原则

### 1. SEO至上原则
- 每个页面必须包含完整的SEO配置
- 使用Next.js的generateMetadata进行动态SEO
- 实现正确的标题层级结构 (h1 > h2 > h3)
- 包含结构化数据(JSON-LD)以获得富媒体摘要
- 优化核心网络指标(Core Web Vitals)
- 实现完整的站点地图和robots.txt

### 2. 性能优化原则
- 所有图片使用Next.js Image组件，配置适当的sizes属性
- 使用动态导入(dynamic import)分割代码
- 实现适当的缓存策略(ISR + Redis)
- 使用Suspense边界处理加载状态
- 预加载关键资源，延迟加载非关键内容

### 3. 多语言架构原则
- 使用next-intl进行国际化，支持多语言扩展策略
- 实现SEO友好的URL结构：/[locale]/[category]/[slug]
- 正确配置hreflang标签
- 为每种语言提供独立的内容和SEO元数据
- 实现语言回退机制

### 4. 代码质量原则
- 严格的TypeScript配置，启用所有严格检查
- 使用ESLint + Prettier保持代码风格一致
- 实现完整的错误边界和错误处理
- 遵循组件单一职责原则
- 使用自定义Hooks抽象业务逻辑

## 技术栈架构

### 前端框架
- **Next.js 14 (App Router)** - SSR/SSG混合模式，SEO友好
- **TypeScript** - 严格类型检查，提升代码质量
- **Tailwind CSS** - 响应式设计，快速样式开发
- **Framer Motion** - 优雅的页面动画和过渡效果
- **React Hook Form** - 表单处理和验证
- **Zustand** - 轻量级状态管理

### 后端技术
- **Next.js API Routes** - 服务端逻辑处理
- **PostgreSQL** - 主数据库，支持复杂查询
- **Prisma ORM** - 数据库操作和类型安全
- **Redis** - 缓存和会话管理
- **CloudFlare** - CDN和安全防护

### AI集成技术
- **通义千问 (Qwen)** - 阿里云大语言模型，主要AI服务提供商
- **豆包 (Doubao)** - 字节跳动AI模型，备用服务
- **智谱AI (ZhipuAI)** - 国产AI模型，多语言支持
- **AI服务管理** - 智能路由、负载均衡、故障转移
- **提示词工程** - 专业的玄学领域提示词库

### 内容管理
- **Sanity CMS** - 无头CMS，多语言内容管理
- **MDX** - Markdown + React组件，灵活的内容格式
- **Sharp** - 图片优化和处理

### 部署和监控
- **Vercel** - 部署平台，边缘计算优化
- **Sentry** - 错误监控和性能分析
- **Umami** - 开源Web分析，隐私友好
- **Uptime Robot** - 网站监控

## 多语言扩展策略

### 第一阶段：核心市场语言 (立即实施)
```typescript
const PHASE_1_LANGUAGES = {
  en: 'English',     // 全球通用语，最大市场
  zh: '中文',         // 中国+华人市场 (14亿人口)
  es: 'Español',     // 西班牙+拉美市场 (5亿人口)
  pt: 'Português',   // 巴西+葡语区 (2.6亿人口)
  hi: 'हिन्दी',       // 印度北部核心市场 (6亿人口)
  ja: '日本語',       // 日本高消费市场 (1.25亿人口)
};
```

### 第二阶段：重要区域语言 (6个月后)
```typescript
const PHASE_2_LANGUAGES = {
  de: 'Deutsch',     // 德国+德语区 (1亿人口)
  fr: 'Français',    // 法国+法语区 (2.8亿人口)
  it: 'Italiano',    // 意大利传统占星市场 (6500万人口)
  ru: 'Русский',     // 俄罗斯+东欧 (2.6亿人口)
  ko: '한국어',       // 韩国新兴市场 (5100万人口)
  ar: 'العربية',     // 阿拉伯世界 (4.2亿人口)
};
```

## 项目结构设计

```
mystical-website/
├── README.md
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── package.json
├── prisma/
│   ├── schema.prisma
│   ├── migrations/
│   └── seed.ts
├── public/
│   ├── images/
│   ├── icons/
│   └── sitemap.xml
├── src/
│   ├── app/
│   │   ├── [locale]/
│   │   │   ├── layout.tsx
│   │   │   ├── page.tsx
│   │   │   ├── blog/                    # 博客模块
│   │   │   ├── products/                # 商品模块
│   │   │   ├── tarot/                   # 塔罗专题
│   │   │   ├── astrology/               # 星座专题
│   │   │   ├── natal-chart/             # 星盘专题
│   │   │   ├── numerology/              # 数字命理
│   │   │   ├── tests/                   # 在线测试中心
│   │   │   └── admin/                   # 管理后台
│   │   ├── api/                         # API路由
│   │   └── middleware.ts
│   ├── components/
│   │   ├── ui/                          # 基础UI组件
│   │   ├── layout/                      # 布局组件
│   │   ├── seo/                         # SEO组件
│   │   ├── blog/                        # 博客组件
│   │   ├── products/                    # 商品组件
│   │   ├── mystical/                    # 玄学专用组件
│   │   ├── tests/                       # 在线测试组件
│   │   └── ai/                          # AI集成组件
│   ├── lib/                             # 工具库
│   ├── hooks/                           # 自定义Hooks
│   ├── stores/                          # 状态管理
│   ├── types/                           # TypeScript类型
│   ├── styles/                          # 样式文件
│   └── data/                            # 静态数据
├── messages/                            # 国际化文件
│   ├── en.json                          # 英语（全球通用）
│   ├── zh.json                          # 中文（中国+华人市场）
│   ├── es.json                          # 西班牙语（西班牙+拉美）
│   ├── pt.json                          # 葡萄牙语（巴西+葡语区）
│   ├── hi.json                          # 印地语（印度核心市场）
│   ├── ja.json                          # 日语（日本高消费市场）
│   ├── de.json                          # 德语（德国+德语区）
│   ├── fr.json                          # 法语（法国+法语区）
│   ├── it.json                          # 意大利语（传统占星市场）
│   ├── ru.json                          # 俄语（俄罗斯+东欧）
│   ├── ko.json                          # 韩语（韩国新兴市场）
│   └── ar.json                          # 阿拉伯语（阿拉伯世界）
└── docs/                               # 项目文档
    ├── api.md
    ├── deployment.md
    └── content-guidelines.md
```

## 商业化策略

### 收入模式设计
1. **内容变现** - 付费深度解读文章、会员订阅服务、在线玄学课程
2. **商品销售** - 塔罗牌套装、水晶饰品、玄学书籍、占星配件
3. **服务收费** - 个人占卜服务、星盘分析服务、玄学咨询
4. **外链服务（终极目标）** - 客座文章发布、高质量外链、内容营销服务

### SEO权威性建设路径
- **第一阶段**：内容建设 (0-6个月) - 500+优质文章，10万+月访问量，DA 20+
- **第二阶段**：权威确立 (6-18个月) - 1000+文章，50万+月访问量，DA 40+
- **第三阶段**：商业变现 (18个月+) - DA 60+，100万+月访问量，外链服务

## 专门规则文件引用

当需要开发特定功能模块时，请引用对应的专门规则文件：

- **前端设计系统**: `.augment/rules/01-frontend-design-rules.md`
- **组件架构设计**: `.augment/rules/02-component-architecture-rules.md`
- **博文管理系统**: `.augment/rules/03-blog-management-rules.md`
- **在线测试功能**: `.augment/rules/04-online-tests-rules.md`
- **商品管理系统**: `.augment/rules/05-product-management-rules.md`
- **AI服务集成**: `.augment/rules/06-ai-integration-rules.md`
- **数据库设计**: `.augment/rules/07-database-design-rules.md`
- **API设计规范**: `.augment/rules/08-api-design-rules.md`
- **部署和监控**: `.augment/rules/09-deployment-monitoring-rules.md`

## 开发工作流

### 代码质量控制
- 使用Husky实现Git Hooks
- pre-commit代码格式化和静态检查
- 代码审查机制
- 自动化测试集成

### 版本控制策略
- 功能分支开发模式
- 语义化版本控制
- 变更日志维护
- 回滚策略预案

记住：这个项目的核心目标是通过高质量内容建立SEO权威性，最终发展为可持续的外链服务业务。每个技术决策都应该服务于这个商业目标。
